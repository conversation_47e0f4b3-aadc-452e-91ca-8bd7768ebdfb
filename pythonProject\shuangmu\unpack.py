import struct
import numpy as np

# 定义字节串
# 使用 struct.unpack 解析字节为 double
rms_bytes = b',\xa2\x0fO\x04\xbb\x9d?\xac\x05\xdf\xfeGK\xa3\xbf\xb4_\xc4\xf9#T\xee?k\xaa\x86U\x19E\xd4\xbf\xad\xdf\xadT\xb4\xd5\xef\xbf\x16\'\x94\x1b{Sw\xbf\xdcn6#\x82\xf0\xb9?vo\xd2$z\x1f\xb8?\xf1Y\x16\x03\xdeh\xd4?uC \xe1\xcf-\xee?\xa9\x1b\xbc\xf4\xcb\xa5v@\xde\x8a\x9c\xb3\xb6\xfaZ@o\x86\xe3\x046\xfd\xa3\xc0'  # 示例字节串
print(len(rms_bytes))
# 存储解析后的 double 值的列表
double_values = []

# 每次解析 8 个字节
for i in range(0, len(rms_bytes), 8):
    # 获取当前 8 字节片段
    byte_segment = rms_bytes[i:i + 8]
    # 确保该片段刚好有 8 个字节
    if len(byte_segment) == 8:
        # 解析为 double 并存储
        double_value = struct.unpack('d', byte_segment)[0]
        double_values.append(double_value)
    else:
        print(f"数据不足以形成完整的 double: {byte_segment}")

# 打印解析后的 double 值
print("解析得到的 double 值:", double_values)


def rotation_matrix_to_quaternion(tempRT):
    # 假设 tempRT 是一个包含的 double 的数组
    # 取出第2到第10个元素，并假设这些元素组成一个 3x3 的旋转矩阵
    # 注意：Python 索引从 0 开始，这里提取的是索引为 1 到 9 的元素
    R = np.array([
        [tempRT[0], tempRT[1], tempRT[2]],  # 第一行
        [tempRT[3], tempRT[4], tempRT[5]],  # 第二行
        [tempRT[6], tempRT[7], tempRT[8]],  # 第三行
    ])

    q = np.zeros(4)
    trace = R[0, 0] + R[1, 1] + R[2, 2]

    if trace > 0:
        s = np.sqrt(trace + 1.0) * 2  # s=4*Q0
        q[0] = 0.25 * s  # Q0
        q[1] = (R[2, 1] - R[1, 2]) / s  # Qx
        q[2] = (R[0, 2] - R[2, 0]) / s  # Qy
        q[3] = (R[1, 0] - R[0, 1]) / s  # Qz
    else:
        print('error')

    return q


# 计算并输出角度
q = rotation_matrix_to_quaternion(double_values[1:10])
print("解析得到的坐标值：")
print(f"RMS:{double_values[0]:.2f};Tx: {double_values[10]:.2f};Ty: {double_values[11]:.2f};Tz: {double_values[12]:.2f}")
print(f"Q0:{q[0]:.2f};Qx: {q[1]:.2f};Qy: {q[2]:.2f};Qz: {q[3]:.2f}")

# print("RMS Value:", rms_value)
