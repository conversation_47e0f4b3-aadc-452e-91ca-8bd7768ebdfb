from PyQt5.QtWidgets import (
    QApplication, QWidget, QGroupBox, QGridLayout, QLabel, QComboBox,
    QLineEdit, QCheckBox, QPushButton, QSpacerItem, QSizePolicy, QHBoxLayout, QVBoxLayout
)


class step4_ui(QWidget):
    def __init__(self,parent=None):
        super().__init__(parent)
        self.layout_ = QHBoxLayout(self)
        self.left_widget = QWidget(self)
        self.left_layout = QVBoxLayout(self.left_widget)
        self.layout_.addWidget(self.left_widget, 4)

        self.find_normal_widget = QGroupBox("试件寻法", self.left_widget)
        self.find_normal_layout = QGridLayout(self.find_normal_widget)
        self.left_layout.addWidget(self.find_normal_widget, 3)




        self.right_widget = QWidget(self)
        self.right_layout = QVBoxLayout(self.right_widget)
        self.layout_.addWidget(self.right_widget, 2)
