from PyQt5.QtGui import QFont, QPixmap
from PyQt5.QtWidgets import (
    QWidget, QPushButton, QSpinBox, QDoubleSpinBox, QLabel, QFormLayout, QHBoxLayout,
    QVBoxLayout, QApplication, QTabWidget, QSizePolicy, QGroupBox, QGridLayout, QComboBox, QSpacerItem, QTableWidget,
    QLineEdit
)
from config import *
from PyQt5.QtCore import Qt



class step2_ui(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)

        self.layout_ = QHBoxLayout(self)
        self.left_widget = QWidget(self)
        self.left_layout = QVBoxLayout(self.left_widget)
        self.layout_.addWidget(self.left_widget, 4)

        self.tool_parameter_widget=QGroupBox("刀具参数",self.left_widget)
        self.tool_parameter_layout=QGridLayout(self.tool_parameter_widget)
        self.left_layout.addWidget(self.tool_parameter_widget,7)

        self.tcp = QLineEdit(self.tool_parameter_widget)
        self.saved_tcp = QLineEdit(self.tool_parameter_widget)
        self.save_tcp_but = QPushButton("保存TCP参数", self.tool_parameter_widget)

        self.tool_parameter_layout.addWidget(QLabel("TCP(换刀后手动标定并保存)"),0,0)
        self.tool_parameter_layout.addWidget(self.tcp,0,1)
        self.tool_parameter_layout.addWidget(self.saved_tcp,0,2)
        self.tool_parameter_layout.addWidget(self.save_tcp_but,0,6)

        self.show_tool_image_widget = QLabel(self.tool_parameter_widget)
        self.show_tool_image_widget.setAlignment(Qt.AlignCenter)
        self.tool_image = QPixmap("img.png")
        scaled_pixmap = self.tool_image.scaledToHeight(200, Qt.SmoothTransformation)
        self.show_tool_image_widget.setPixmap(scaled_pixmap)
        self.tool_parameter_layout.addWidget(self.show_tool_image_widget, 1,0,3,7)


        self.choose_tool_combobox=QComboBox(self.tool_parameter_widget)

        self.tool_install_len = QLineEdit(self.tool_parameter_widget)
        self.saved_tool_install_len = QLineEdit(self.tool_parameter_widget)

        self.blade_len = QLineEdit(self.tool_parameter_widget)
        self.saved_blade_len = QLineEdit(self.tool_parameter_widget)

        self.foot_to_work_len = QLineEdit(self.tool_parameter_widget)
        self.saved_foot_to_work_len = QLineEdit(self.tool_parameter_widget)

        self.min_drill_len = QLineEdit(self.tool_parameter_widget)
        self.saved_min_drill_len = QLineEdit(self.tool_parameter_widget)

        for text in {self.saved_tool_install_len,self.saved_blade_len,
                     self.saved_foot_to_work_len,self.saved_min_drill_len}:
            text.setReadOnly(True)
            text.setStyleSheet("background-color: #f5f5f5;")

        self.save_tool_parameter_but=QPushButton("保存刀具参数",self.tool_parameter_widget)

        self.tool_parameter_layout.addWidget(QLabel("刀具选择"),4,0)
        self.tool_parameter_layout.addWidget(self.choose_tool_combobox, 4, 1)
        self.tool_parameter_layout.addWidget(QLabel("装刀长度(mm)"),5,0)
        self.tool_parameter_layout.addWidget(self.tool_install_len, 5, 1)
        self.tool_parameter_layout.addWidget(self.saved_tool_install_len, 5, 2)
        self.tool_parameter_layout.addWidget(QLabel("刃长(mm)"),5,3)
        self.tool_parameter_layout.addWidget(self.blade_len, 5, 4)
        self.tool_parameter_layout.addWidget(self.saved_blade_len, 5, 5)
        self.tool_parameter_layout.addWidget(QLabel("压力脚至工件距离(0-50mm)"), 6, 0)
        self.tool_parameter_layout.addWidget(self.foot_to_work_len, 6, 1)
        self.tool_parameter_layout.addWidget(self.saved_foot_to_work_len, 6, 2)
        self.tool_parameter_layout.addWidget(QLabel("最小钻穿长度(mm)"), 6, 3)
        self.tool_parameter_layout.addWidget(self.min_drill_len, 6, 4)
        self.tool_parameter_layout.addWidget(self.saved_min_drill_len, 6, 5)
        self.tool_parameter_layout.addWidget(self.save_tool_parameter_but, 6, 6)

        for te in self.tool_parameter_widget.findChildren(QLineEdit):
            te.setMaximumHeight(40)

        self.robot_set_widget = QGroupBox("移动机械臂", self.left_widget)
        self.robot_set_layout = QGridLayout(self.robot_set_widget)
        self.left_layout.addWidget(self.robot_set_widget, 5)

        # 先把所有列都设置为相同的伸缩因子
        # for col in range(0, 84):
        #     print(col)
        #     self.robot_set_layout.setColumnStretch(col, 1)
        # for row in range(0, 5):
        #     self.robot_set_layout.setRowStretch(row, 1)

        # 再添加那些要居中显示的 QLabel，依然传入 Qt.AlignCenter
        self.robot_set_layout.addWidget(QLabel("X"), 0, 12, 1, 10, Qt.AlignCenter)
        self.robot_set_layout.addWidget(QLabel("Y"), 0, 22, 1, 10, Qt.AlignCenter)
        self.robot_set_layout.addWidget(QLabel("Z"), 0, 32, 1, 10, Qt.AlignCenter)
        self.robot_set_layout.addWidget(QLabel("A"), 0, 42, 1, 10, Qt.AlignCenter)
        self.robot_set_layout.addWidget(QLabel("B"), 0, 52, 1, 10, Qt.AlignCenter)
        self.robot_set_layout.addWidget(QLabel("C"), 0, 62, 1, 10, Qt.AlignCenter)

        # 下面几行保持不变
        self.robot_set_layout.addWidget(QLabel("机器人试钻位置"), 1, 0, 1, 12)
        self.robot_set_layout.addWidget(QLabel("机器人零位"), 2, 0, 1, 12)
        self.robot_set_layout.addWidget(QLabel("机器人换刀位"), 3, 0, 1, 12)

        self.robot_drill_fields = robot_pose_set_widget(self.robot_set_widget)
        self.robot_zero_fields = robot_pose_set_widget(self.robot_set_widget)
        self.robot_change_fields = robot_pose_set_widget(self.robot_set_widget)

        self.robot_set_layout.addWidget(self.robot_drill_fields.x_text,  1, 12, 1, 10)
        self.robot_set_layout.addWidget(self.robot_drill_fields.y_text,  1, 22, 1, 10)
        self.robot_set_layout.addWidget(self.robot_drill_fields.z_text,  1, 32, 1, 10)
        self.robot_set_layout.addWidget(self.robot_drill_fields.a_text,  1, 42, 1, 10)
        self.robot_set_layout.addWidget(self.robot_drill_fields.b_text,  1, 52, 1, 10)
        self.robot_set_layout.addWidget(self.robot_drill_fields.c_text,  1, 62, 1, 10)

        self.robot_set_layout.addWidget(self.robot_zero_fields.x_text,   2, 12, 1, 10)
        self.robot_set_layout.addWidget(self.robot_zero_fields.y_text,   2, 22, 1, 10)
        self.robot_set_layout.addWidget(self.robot_zero_fields.z_text,   2, 32, 1, 10)
        self.robot_set_layout.addWidget(self.robot_zero_fields.a_text,   2, 42, 1, 10)
        self.robot_set_layout.addWidget(self.robot_zero_fields.b_text,   2, 52, 1, 10)
        self.robot_set_layout.addWidget(self.robot_zero_fields.c_text,   2, 62, 1, 10)

        self.robot_set_layout.addWidget(self.robot_change_fields.x_text,3, 12, 1, 10)
        self.robot_set_layout.addWidget(self.robot_change_fields.y_text,3, 22, 1, 10)
        self.robot_set_layout.addWidget(self.robot_change_fields.z_text,3, 32, 1, 10)
        self.robot_set_layout.addWidget(self.robot_change_fields.a_text,3, 42, 1, 10)
        self.robot_set_layout.addWidget(self.robot_change_fields.b_text,3, 52, 1, 10)
        self.robot_set_layout.addWidget(self.robot_change_fields.c_text,3, 62, 1, 10)

        self.robot_set_save_but = QPushButton("加载理论位置", self.robot_set_widget)
        self.robot_set_layout.addWidget(self.robot_set_save_but, 3, 72, 1, 12)

        self.move_robot_drill_but = QPushButton("移动机器人至试钻位置", self.robot_set_widget)
        self.move_robot_zero_but = QPushButton("移动机器人至零点", self.robot_set_widget)
        self.move_robot_change_but = QPushButton("移动机器人至换刀位置", self.robot_set_widget)
        self.generate_move_str_but = QPushButton("生成加工程序", self.robot_set_widget)

        self.robot_set_layout.addWidget(self.move_robot_drill_but, 4, 0, 1, 21)
        self.robot_set_layout.addWidget(self.move_robot_zero_but, 4, 21, 1, 21)
        self.robot_set_layout.addWidget(self.move_robot_change_but, 4, 42, 1, 21)
        self.robot_set_layout.addWidget(self.generate_move_str_but, 4, 63, 1, 21)


        self.right_widget = QWidget(self)
        self.right_layout=QVBoxLayout(self.right_widget)
        self.layout_.addWidget(self.right_widget,2)


class robot_pose_set_widget():
    def __init__(self, parent_widget):
        self.x_text = QLineEdit(parent_widget)
        self.y_text = QLineEdit(parent_widget)
        self.z_text = QLineEdit(parent_widget)
        self.a_text = QLineEdit(parent_widget)
        self.b_text = QLineEdit(parent_widget)
        self.c_text = QLineEdit(parent_widget)

        for te in (
            self.x_text, self.y_text, self.z_text,
            self.a_text, self.b_text, self.c_text
        ):
            te.setFixedHeight(40)  # 单行高度，按需调整
            te.setMaximumWidth(200)





