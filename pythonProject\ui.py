from PyQt5.QtCore import pyqtSlot, QTimer
from PyQt5.QtGui import QFont
from PyQt5.QtWidgets import (
    QMainWindow, QApplication,
    QWidget, QVBoxLayout, QStackedWidget
)
from config import *
from drill_ui import drill_ui
from main_ui import main_ui, clear_all_margins
from robot_connect import RobotClient
import time

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()

        # 1. 创建 central widget 并设置为 MainWindow 的中央控件
        central = QWidget(self)
        self.setCentralWidget(central)

        # 2. 给 central 一个垂直布局，把 stack 放进去
        central_layout = QVBoxLayout(central)
        self.stack = QStackedWidget(central)
        central_layout.addWidget(self.stack)

        self.main_ui = main_ui()
        self.drill_ui = drill_ui()

        self.stack.addWidget(self.main_ui)   # index 0
        self.stack.addWidget(self.drill_ui)  # index 1
        self.stack.setCurrentIndex(0)
        

        self.robot=RobotClient(ROBOT_IP,ROBOT_PORT)

        self.main_ui_connect()
        self.drill_ui_connect()

    def main_ui_connect(self):

        self.main_ui.robot_connect_but.clicked.connect(self.robot.connect_to_server)
        self.robot.connected.connect(self.on_connected)
        self.robot.disconnected.connect(self.on_disconnected)
        self.robot.errorOccurred.connect(self.on_error)
        # self.robot.responseReceived.connect(self.on_response)
        self.main_ui.hole_making_but.clicked.connect(
            lambda: self.stack.setCurrentIndex(1)
        )


    def drill_ui_connect(self):

        self.drill_ui.main_interface_but.clicked.connect(
            lambda: self.stack.setCurrentIndex(0)
        )

        for w in [self.drill_ui.step1_widget.spin_rows, self.drill_ui.step1_widget.spin_cols,
                  self.drill_ui.step1_widget.spin_dw, self.drill_ui.step1_widget.spin_dh,
                  self.drill_ui.step1_widget.spin_hs, self.drill_ui.step1_widget.spin_vs,
                  self.drill_ui.step1_widget.spin_dia]:
            w.valueChanged.connect(self.update_params)

        self.drill_ui.step1_widget.restore_buttorn.clicked.connect(self.restore_defaults)
        self.drill_ui.step1_widget.select_all_buttorn.clicked.connect(
            self.drill_ui.step1_widget.left_grid_widget.all_selection)
        self.drill_ui.step1_widget.select_inv_buttorn.clicked.connect(
            self.drill_ui.step1_widget.left_grid_widget.invert_selection)

        self.drill_ui.previous_step_but.setEnabled(False)
        self.drill_ui.previous_step_but.clicked.connect(self.go_prev)
        self.drill_ui.next_step_but.clicked.connect(self.go_next)
        self.drill_ui.tab1_widget.currentChanged.connect(self.update_but_states)


    def restore_defaults(self):
        self.drill_ui.step1_widget.spin_rows.setValue(DEFAULT_ROWS)
        self.drill_ui.step1_widget.spin_cols.setValue(DEFAULT_COLS)
        self.drill_ui.step1_widget.spin_dw.setValue(DEFAULT_DRILL_W)
        self.drill_ui.step1_widget.spin_dh.setValue(DEFAULT_DRILL_H)
        self.drill_ui.step1_widget.spin_hs.setValue(DEFAULT_SPACING)
        self.drill_ui.step1_widget.spin_vs.setValue(DEFAULT_SPACING)
        self.drill_ui.step1_widget.spin_dia.setValue(DEFAULT_DIAMETER)
        self.drill_ui.step1_widget.left_grid_widget.restore_defaults()

    def update_params(self):
        self.drill_ui.step1_widget.left_grid_widget.set_parameters(
            self.drill_ui.step1_widget.spin_rows.value(), self.drill_ui.step1_widget.spin_cols.value(),
            self.drill_ui.step1_widget.spin_dw.value(), self.drill_ui.step1_widget.spin_dh.value(),
            self.drill_ui.step1_widget.spin_hs.value(), self.drill_ui.step1_widget.spin_vs.value(),
            self.drill_ui.step1_widget.spin_dia.value()
        )

    def go_prev(self):
        """切换到上一页"""
        current_index = self.drill_ui.tab1_widget.currentIndex()
        self.drill_ui.tab1_widget.setCurrentIndex(current_index - 1)

    def go_next(self):
        """切换到下一页"""
        current_index = self.drill_ui.tab1_widget.currentIndex()
        self.drill_ui.tab1_widget.setCurrentIndex(current_index + 1)

    def update_but_states(self,index):

        self.drill_ui.previous_step_but.setEnabled(index > 0)
        if (index==4):
            self.drill_ui.next_step_but.setEnabled(0)
        else:
            self.drill_ui.next_step_but.setEnabled(1)



    @pyqtSlot()
    def on_connected(self):
        """捕获到 connected 信号后的处理"""
        self.main_ui.robot_connect_but.setStyleSheet("background-color: green;")

    @pyqtSlot()
    def on_disconnected(self):

        self.main_ui.robot_connect_but.setStyleSheet("background-color: red;")

    @pyqtSlot()
    def on_error(self):

        self.main_ui.robot_connect_but.setStyleSheet("background-color: red;")
        self.drill_ui.robot_connect_but.setStyleSheet("background-color: red;")


if __name__ == "__main__":
    import sys

    start_time = time.time()
    app = QApplication(sys.argv)
    app.setFont(QFont("Microsoft YaHei", 16))

    w = MainWindow()
    clear_all_margins(w)
    def measure():
        elapsed = time.time() - start_time
        print(f"界面显示所用时间：{elapsed:.3f} 秒")
    w.showMaximized()
    QTimer.singleShot(0, measure)
    # w.show()
    sys.exit(app.exec_())
