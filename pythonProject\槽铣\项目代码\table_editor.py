# table_editor.py
import sys
import os
import csv
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QTableWidget, QTableWidgetItem,
    QAction, QFileDialog, QMessageBox, QVBoxLayout, QHBoxLayout, QPushButton,
    QAbstractItemView
)
from PyQt5.QtCore import Qt, pyqtSignal

class TableEditorWindow(QMainWindow):
    # 自定义信号：确认后传出选中行数据（list）
    rowConfirmed = pyqtSignal(list)

    def __init__(self):
        super().__init__()
        self.setWindowTitle("刀库")
        self.resize(800, 600)

        # 默认模板文件
        self.default_template = "1.csv"

        central = QWidget(self)
        self.setCentralWidget(central)
        layout = QVBoxLayout(central)

        # 表格
        self.table = QTableWidget(10, 9, self)
        self.table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.table.setSelectionMode(QAbstractItemView.SingleSelection)
        layout.addWidget(self.table)
# 设置列名
        column_headers = ["ID", "刀具编号", "直径（mm）", "锪刀长度（mm）", "钻头长度（mm）", "最小钻穿（mm）", "锪窝深度（mm）", "装刀长度", "刃长"]
        self.table.setHorizontalHeaderLabels(column_headers)
# 填充数据
        self.populate_table_with_data()

        # 调整单元格大小以适应内容
        self.table.resizeColumnsToContents()

        # 调整窗口大小
        self.resize(1000, 600)

# 在第二行填充数据
        # 按钮
        btn_layout = QHBoxLayout()
        self.edit_btn = QPushButton("编辑")
        self.confirm_btn = QPushButton("确认")
        self.save_btn = QPushButton("保存")
        self.add_row_btn = QPushButton("增行")
        self.del_row_btn = QPushButton("删行")
        self.add_col_btn = QPushButton("增列")
        self.del_col_btn = QPushButton("删列")
        for btn in [self.confirm_btn,self.edit_btn,  self.save_btn,
                    self.add_row_btn, self.del_row_btn,
                    self.add_col_btn, self.del_col_btn]:
            btn_layout.addWidget(btn)
        layout.addLayout(btn_layout)

        # 信号连接
        self.edit_btn.clicked.connect(self.enter_edit_mode)
        self.confirm_btn.clicked.connect(self.confirm_selection)
        self.save_btn.clicked.connect(self.exit_edit_mode)
        self.add_row_btn.clicked.connect(self.add_row)
        self.del_row_btn.clicked.connect(self.delete_row)
        self.add_col_btn.clicked.connect(self.add_column)
        self.del_col_btn.clicked.connect(self.delete_column)

        self.filename = None
        self.editing = False

        # 菜单
        menubar = self.menuBar()
        fileMenu = menubar.addMenu("文件")
        openAction = QAction("打开", self)
        openAction.triggered.connect(self.openFile)
        fileMenu.addAction(openAction)
        saveAction = QAction("另存为", self)
        saveAction.triggered.connect(self.saveFileAs)
        fileMenu.addAction(saveAction)
        exitAction = QAction("退出", self)
        exitAction.triggered.connect(self.close)
        fileMenu.addAction(exitAction)

        # 启动加载模板
        self.load_template()

    def disable_first_row_selection(self):
        if self.table.rowCount() > 0:
            for col in range(self.table.columnCount()):
                item = self.table.item(0, col)
                if not item:
                    item = QTableWidgetItem("")
                    self.table.setItem(0, col, item)
                item.setFlags(item.flags() & ~Qt.ItemIsSelectable)

    def enable_first_row_selection(self):
        if self.table.rowCount() > 0:
            for col in range(self.table.columnCount()):
                item = self.table.item(0, col)
                if item:
                    item.setFlags(item.flags() | Qt.ItemIsSelectable)

    def load_template(self):
        if os.path.exists(self.default_template):
            self.load_from_path(self.default_template)
            self.filename = None
            self.disable_first_row_selection()
            self.statusBar().showMessage(f"已加载模板 {self.default_template}")
        else:
            self.disable_first_row_selection()
            self.statusBar().showMessage("未找到模板文件，使用空表格")

    def enter_edit_mode(self):
        self.table.setEditTriggers(QAbstractItemView.AllEditTriggers)
        self.editing = True
        self.enable_first_row_selection()
        self.statusBar().showMessage("已进入编辑模式")

    def exit_edit_mode(self):
        if not self.editing:
            QMessageBox.information(self, "提示", "当前不在编辑模式，无需保存。")
            return
        if self.filename:
            self._save_to_path(self.filename)
        else:
            self.saveFileAs()
        self.table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.editing = False
        self.disable_first_row_selection()
        self.statusBar().showMessage("已保存并退出编辑模式")

    def confirm_selection(self):
        if self.editing:
            QMessageBox.warning(self, "提示", "请先退出编辑模式，再确认选中行。")
            return
        # 使用 selectionModel 确保用户已手动选中
        selected = self.table.selectionModel().selectedRows()
        if not selected:
            QMessageBox.warning(self, "提示", "请先选择一行。")
            return
        row = selected[0].row()
        data = [self.table.item(row, c).text() if self.table.item(row, c) else ""
                for c in range(self.table.columnCount())]
        self.rowConfirmed.emit(data)
        self.close()

    def add_row(self):
        if not self.editing:
            QMessageBox.warning(self, "提示", "请在编辑模式下操作。")
            return
        self.table.insertRow(self.table.rowCount())

    def delete_row(self):
        if not self.editing:
            QMessageBox.warning(self, "提示", "请在编辑模式下操作。")
            return
        row = self.table.currentRow()
        if row >= 0:
            self.table.removeRow(row)
        else:
            QMessageBox.warning(self, "提示", "请先选中要删除的行。")

    def add_column(self):
        if not self.editing:
            QMessageBox.warning(self, "提示", "请在编辑模式下操作。")
            return
        self.table.insertColumn(self.table.columnCount())

    def delete_column(self):
        if not self.editing:
            QMessageBox.warning(self, "提示", "请在编辑模式下操作。")
            return
        col = self.table.currentColumn()
        if col >= 0:
            self.table.removeColumn(col)
        else:
            QMessageBox.warning(self, "提示", "请先选中要删除的列。")

    def populate_table_with_data(self):
        data = [
            ["1", "TiNO.0", "9.4000", "0.0000", "3.1234", "1.5678", "0.0000", "65.0000", "65.0000"],
            ["2", "TiNO.1", "6.2000", "1.0000", "4.5678", "2.3456", "0.0000", "65.0000", "65.0000"],
            ["3", "TiNO3.1", "3.1000", "2.0000", "5.6789", "3.4567", "2.0000", "65.0000", "65.0000"],
            ["4", "ToolNO5.1", "5.1000", "3.0000", "6.7890", "4.5678", "2.0000", "65.0000", "65.0000"],
            ["5", "ToolNO3.1", "3.1000", "4.4500", "7.8901", "5.6789", "2.0000", "65.0000", "65.0000"],
            ["6", "ToolNO6.2", "6.2000", "5.0000", "8.9012", "6.7890", "2.0000", "65.0000", "65.0000"],
            ["7", "ToolNO8.2", "8.2000", "6.0000", "9.0123", "7.8901", "7.0000", "65.0000", "65.0000"],
            ["8", "ToolNO4.1", "4.1000", "7.0000", "3.0123", "8.9012", "1.0000", "65.0000", "65.0000"],
            ["9", "ST4.1", "6.2000", "31.0000", "4.1234", "9.0123", "8.0000", "65.0000", "65.0000"],
            ["10", "ST4.2", "9.4000", "9.0000", "5.2345", "1.6789", "3.0000", "65.0000", "65.0000"]
        ]
        self.table.setRowCount(len(data))
        self.table.setColumnCount(len(data[0]))
        for i, row_data in enumerate(data):
            for j, item_data in enumerate(row_data):
                item = QTableWidgetItem(item_data)
                item.setTextAlignment(Qt.AlignCenter) # 设置文本居中对齐
                self.table.setItem(i, j, item)

    def openFile(self):
        path, _ = QFileDialog.getOpenFileName(self, "打开CSV文件", "", "CSV Files (*.csv);;All Files (*)")
        if path:
            self.load_from_path(path)
            self.filename = path
            self.disable_first_row_selection()
            self.statusBar().showMessage(f"已打开 {path}")

    def load_from_path(self, path):
        try:
            with open(path, newline='', encoding='utf-8') as f:
                data = list(csv.reader(f))
            rows = len(data)
            cols = max(len(r) for r in data) if rows else 0
            self.table.setRowCount(rows)
            self.table.setColumnCount(cols)
            for i, r in enumerate(data):
                for j, v in enumerate(r):
                    self.table.setItem(i, j, QTableWidgetItem(v))
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法加载文件: {e}")

    def saveFileAs(self):
        path, _ = QFileDialog.getSaveFileName(self, "保存CSV文件", "", "CSV Files (*.csv);;All Files (*)")
        if path:
            if not path.endswith(".csv"):
                path += ".csv"
            self.filename = path
            self._save_to_path(path)

    def _save_to_path(self, path):
        try:
            rows = self.table.rowCount()
            cols = self.table.columnCount()
            with open(path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                for i in range(rows):
                    row_data = [self.table.item(i, j).text() if self.table.item(i, j) else "" for j in range(cols)]
                    writer.writerow(row_data)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法保存文件: {e}")
