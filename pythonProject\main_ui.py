from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
from PyQt5.QtWidgets import Q<PERSON>ain<PERSON><PERSON>ow, QWidget, QHBoxLayout, QVBoxLayout, QLabel, QApplication, QPushButton, \
    QTextEdit, QSizePolicy, QGridLayout, QGroupBox


class main_ui(QWidget):
    def __init__(self,parent=None):
        super().__init__(parent)

        self.layout_=QVBoxLayout(self)

        self.title_label=QLabel(self)
        self.title_label.setText("重载机器人上位机控制软件")
        title_label_font = QFont("Microsoft YaHei", 28)
        self.title_label.setFont(title_label_font)
        self.layout_.addWidget(self.title_label,2,Qt.AlignCenter)

        self.up_widget=QWidget(self)
        self.up_layout=QHBoxLayout(self.up_widget)
        self.layout_.addWidget(self.up_widget,4)

        self.main_option_widget=QWidget(self.up_widget)
        self.main_option_layout=QGridLayout(self.main_option_widget)
        self.up_layout.addWidget(self.main_option_widget,5)

        self.robot_connect_but=QPushButton("机械臂通信")
        self.sys_alarm_but = QPushButton("系统报警")
        self.auto_mode_but = QPushButton("自动模式")
        self.accessibility_but = QPushButton("辅助功能")
        self.sys_stop_but = QPushButton("系统急停")
        self.alarm_reset_but= QPushButton("报警消除")
        self.manual_mode_but = QPushButton("手动模式")
        self.expert_mode_but = QPushButton("专家模式")
        self.main_option_layout.addWidget(self.robot_connect_but,0,0)
        self.main_option_layout.addWidget(self.sys_alarm_but,0,1)
        self.main_option_layout.addWidget(self.auto_mode_but,0,2)
        self.main_option_layout.addWidget(self.accessibility_but,0,3)
        self.main_option_layout.addWidget(self.sys_stop_but,1,0)
        self.main_option_layout.addWidget(self.alarm_reset_but,1,1)
        self.main_option_layout.addWidget(self.manual_mode_but,1,2)
        self.main_option_layout.addWidget(self.expert_mode_but,1,3)

        self.log_widget=QGroupBox("日志",self.up_widget)
        self.log_layout=QVBoxLayout(self.log_widget)
        self.up_layout.addWidget(self.log_widget,5)

        self.log_text = QTextEdit(self.log_widget)
        self.log_text.setReadOnly(True)
        self.log_layout.addWidget(self.log_text)


        self.down_widget=QWidget(self)
        self.down_layout=QHBoxLayout(self.down_widget)
        self.layout_.addWidget(self.down_widget,4)

        self.down_left_widget=QWidget(self.down_widget)
        self.down_left_layout=QVBoxLayout(self.down_left_widget)
        self.down_layout.addWidget(self.down_left_widget,5)

        self.process_label=QLabel(self.down_left_widget)
        self.process_label.setText("试块加工")
        process_label_font = QFont("Microsoft YaHei", 24)
        self.process_label.setFont(process_label_font)
        self.down_left_layout.addWidget(self.process_label,2,Qt.AlignCenter)

        self.process_select_option_widget = QWidget(self.down_left_widget)
        self.process_select_option_layout = QHBoxLayout(self.process_select_option_widget)
        self.down_left_layout.addWidget(self.process_select_option_widget, 8)

        self.slot_milling_but = QPushButton("槽铣")
        self.trimming_edges_but = QPushButton("切边")
        self.hole_making_but = QPushButton("制孔")

        self.process_select_option_layout.addWidget(self.slot_milling_but, 1)
        self.process_select_option_layout.addWidget(self.trimming_edges_but, 1)
        self.process_select_option_layout.addWidget(self.hole_making_but, 1)


        self.down_right_widget=QWidget(self.down_widget)
        self.down_right_layout=QVBoxLayout(self.down_right_widget)
        self.down_layout.addWidget(self.down_right_widget,5)


def clear_all_margins(widget):
    """递归清除 widget 及其所有子控件的布局边距和间距"""
    lay = widget.layout()
    if lay:
        lay.setContentsMargins(0, 0, 0, 0)
        lay.setSpacing(0)
    for child in widget.findChildren(QWidget):
        clear_all_margins(child)
        child.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

if __name__ == "__main__":
    app = QApplication([])
    font = QFont("Microsoft YaHei", 16)  # 微软雅黑，10 号
    font.setBold(False)
    app.setFont(font)

    win = main_ui()
    clear_all_margins(win)
    win.showMaximized()
    win.show()
    app.exec_()
