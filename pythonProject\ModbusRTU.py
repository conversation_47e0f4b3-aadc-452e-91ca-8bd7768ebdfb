import inspect
import time
import struct
import logging
from pymodbus.client import ModbusSerialClient
from pymodbus.exceptions import ModbusException
import threading

class ModbusSensorReader:
    def __init__(self, client: ModbusSerialClient,
                 slave_address,
                 register_address,
                 register_count=2,
                 byteorder='>',
                 swap_registers=False,
                 retry_on_fail=True,
                 max_retries=3,
                 retry_delay=1.0,
                 lock: threading.Lock = None):
        self.client = client
        self.slave_address = slave_address
        self.register_address = register_address
        self.register_count = register_count
        self.byteorder = byteorder
        self.swap_registers = swap_registers
        self.retry_on_fail = retry_on_fail
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.lock = lock
        self.logger = logging.getLogger(f"{self.__class__.__name__}(ID={slave_address})")
        # 可在 __init__ 中预先检测签名，确定使用 slave 参数：
        sig = inspect.signature(self.client.read_input_registers)
        params = sig.parameters
        self._addr_kw = "slave"

    def _ensure_lock(func):
        def wrapper(self, *args, **kwargs):
            if self.lock:
                with self.lock:
                    return func(self, *args, **kwargs)
            else:
                return func(self, *args, **kwargs)
        return wrapper

    @_ensure_lock
    def read_sensor(self):
        attempt = 0
        # 动态构造关键字参数，如 {'slave': self.slave_address}
        kw = {self._addr_kw: self.slave_address}
        while True:
            attempt += 1
            try:
                # 传入 slave=<从机ID>
                response = self.client.read_input_registers(
                    self.register_address,
                    count=self.register_count,
                    **kw
                )
            except ModbusException as e:
                self.logger.error(f"Modbus 异常: {e}")
                response = None
            except Exception as e:
                self.logger.exception(f"读取异常: {e}")
                response = None

            if response is None:
                self.logger.warning("Modbus 响应 None")
            else:
                if hasattr(response, 'isError') and response.isError():
                    self.logger.error(f"Modbus 响应错误: {response}")
                else:
                    try:
                        registers = response.registers
                        if len(registers) < self.register_count:
                            self.logger.error(f"返回寄存器数 {len(registers)} 少于预期 {self.register_count}")
                        else:
                            regs = registers[:self.register_count]
                            if self.swap_registers:
                                regs = regs[::-1]
                            byte_data = struct.pack(f'{self.byteorder}HH', regs[0], regs[1])
                            value = struct.unpack(f'{self.byteorder}f', byte_data)[0]
                            return value
                    except Exception as e:
                        self.logger.exception(f"解析异常: {e}")

            if not self.retry_on_fail or attempt >= self.max_retries:
                raise IOError(f"Modbus 读取失败 (从机 {self.slave_address}, 尝试 {attempt} 次)")
            else:
                self.logger.info(f"重试: 延迟 {self.retry_delay}s (尝试 {attempt+1})")
                time.sleep(self.retry_delay)


class MultiSensorReader:
    """
    管理多个 Modbus 传感器的轮询读取。
    使用单一 ModbusSerialClient，传入多个 slave_address 列表与对应寄存器地址列表或统一寄存器地址。
    """
    def __init__(self,
                 port,
                 slave_addresses,
                 register_addresses,
                 baudrate=38400,
                 bytesize=8,
                 parity='N',
                 stopbits=1,
                 timeout=1,
                 byteorder='>',
                 swap_registers=False,
                 retry_on_fail=True,
                 max_retries=3,
                 retry_delay=1.0):
        self.logger = logging.getLogger(self.__class__.__name__)
        # 初始化 ModbusSerialClient（已去掉 method 参数，确保与当前 pymodbus 版本兼容）
        try:
            self.client = ModbusSerialClient(
                port=port,
                baudrate=baudrate,
                bytesize=bytesize,
                parity=parity,
                stopbits=stopbits,
                timeout=timeout
            )
            connected = self.client.connect()
            if not connected:
                self.logger.error(f"Modbus 连接失败: port={port}")
                raise ConnectionError(f"无法连接到 Modbus 设备: {port}")
            else:
                self.logger.info(f"成功连接 Modbus: port={port}")
        except Exception as e:
            self.logger.exception(f"Modbus 连接异常: {e}")
            raise

        # 线程锁保证同一串口互斥访问
        self.lock = threading.Lock()

        # 处理 register_addresses 参数：支持列表长度为1时广播
        if isinstance(register_addresses, (list, tuple)):
            if len(register_addresses) == 1:
                # 只有一个地址，广播到所有从机
                self.register_addresses = [register_addresses[0]] * len(slave_addresses)
            elif len(register_addresses) == len(slave_addresses):
                # 列表长度与 slave_addresses 一致，直接对应
                self.register_addresses = list(register_addresses)
            else:
                raise ValueError("register_addresses 列表长度应为1或与 slave_addresses 一致")
        else:
            # 单一地址，重复用于所有传感器
            self.register_addresses = [register_addresses] * len(slave_addresses)

        # 为每个从机创建 ModbusSensorReader（read 方法中使用 device_id/unit，根据 pymodbus 版本调整）
        self.readers = []
        for slave_id, reg_addr in zip(slave_addresses, self.register_addresses):
            reader = ModbusSensorReader(
                client=self.client,
                slave_address=slave_id,
                register_address=reg_addr,
                register_count=2,
                byteorder=byteorder,
                swap_registers=swap_registers,
                retry_on_fail=retry_on_fail,
                max_retries=max_retries,
                retry_delay=retry_delay,
                lock=self.lock
            )
            self.readers.append(reader)

        self._running = False

    def poll_once_all(self):
        """
        对所有传感器依次进行一次读取，返回一个 dict: {slave_id: value 或 异常信息}
        """
        results = {}
        for reader in self.readers:
            sid = reader.slave_address
            try:
                value = reader.read_sensor()
                results[sid] = value
            except Exception as e:
                self.logger.error(f"从机 {sid} 读取失败: {e}")
                results[sid] = None  # 或者记录错误字符串 str(e)
        return results

    def run_loop(self, delay=0.5):
        """
        持续轮询所有传感器，间隔 delay 秒。可自行在外层捕获 KeyboardInterrupt。
        每次轮询可通过回调、信号、日志等方式处理结果。
        这里示例直接打印或日志记录。
        """
        self._running = True
        self.logger.info("开始多传感器轮询")
        try:
            while self._running:
                results = self.poll_once_all()
                # 处理结果：示例打印日志
                # 可以改为回调函数或发送信号给 UI
                parts = []
                for sid, val in results.items():
                    if val is None:
                        parts.append(f"ID={sid}: 读取失败")
                    else:
                        parts.append(f"ID={sid}: {val:.2f}")
                self.logger.info(" | ".join(parts))
                time.sleep(delay)
        except KeyboardInterrupt:
            self.logger.info("检测到 KeyboardInterrupt，退出多传感器轮询")
        finally:
            self.close()
            self.logger.info("多传感器轮询结束，连接已关闭")

    def stop(self):
        self._running = False

    def close(self):
        """
        关闭 Modbus 连接
        """
        try:
            self.client.close()
            self.logger.info("ModbusSerialClient 已关闭")
        except Exception as e:
            self.logger.warning(f"关闭时异常: {e}")

if __name__ == "__main__":
    import argparse
    import sys
    import logging

    # 配置日志
    logging.basicConfig(level=logging.INFO, format="%(asctime)s [%(levelname)s] %(name)s: %(message)s")

    parser = argparse.ArgumentParser(description="多传感器 Modbus 轮询示例")
    parser.add_argument("--port", default="COM11", help="串口设备，例如 COM5 或 /dev/ttyUSB0")
    parser.add_argument("--slaves", nargs="+", type=int, default=[1,2,3,4], help="从机地址列表，例如 1 2 3 4")
    parser.add_argument("--regs", nargs="+", help="寄存器地址列表，可与 slaves 等长，或只指定一个地址", default=["0x18"])
    parser.add_argument("--baudrate", type=int, default=38400)
    parser.add_argument("--parity", default='N', choices=['N','E','O'])
    parser.add_argument("--timeout", type=float, default=1.0)
    parser.add_argument("--byteorder", default='>', choices=['>','<'])
    parser.add_argument("--swap", action="store_true", help="是否交换寄存器顺序")
    parser.add_argument("--delay", type=float, default=0.5, help="轮询间隔（秒）")
    parser.add_argument("--max-retries", type=int, default=3)
    parser.add_argument("--retry-delay", type=float, default=1.0)
    args = parser.parse_args()

    # 解析寄存器地址列表
    reg_addrs = []
    for r in args.regs:
        try:
            reg_addrs.append(int(r, 0))
        except:
            print(f"无法解析寄存器地址: {r}")
            sys.exit(1)

    try:
        msr = MultiSensorReader(
            port=args.port,
            slave_addresses=args.slaves,
            register_addresses=reg_addrs,
            baudrate=args.baudrate,
            parity=args.parity,
            timeout=args.timeout,
            byteorder=args.byteorder,
            swap_registers=args.swap,
            retry_on_fail=True,
            max_retries=args.max_retries,
            retry_delay=args.retry_delay
        )
    except Exception as e:
        print(f"初始化失败: {e}")
        sys.exit(1)

    try:
        msr.run_loop(delay=args.delay)
    finally:
        msr.close()
