import sys
from PyQt5 import uic
from PyQt5 import QtCore
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QLabel, QPushButton,
    QTextEdit, QLineEdit, QComboBox, QTabWidget, QGroupBox,
    QGridLayout, QHBoxLayout, QVBoxLayout, QStackedWidget, QSizePolicy
)
from PyQt5.QtCore import Qt
from table_editor import TableEditorWindow

# 导入资源文件
import 重载机器人.codes_rc




class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.resize(1920, 1080)

        # 创建堆栈容器
        self.stack = QStackedWidget()
        self.setCentralWidget(self.stack)

        # 动态 load UI，返回 QWidget 实例
        self.page_main = uic.loadUi("重载机器人/主界面.ui")   # 这里 page_main.btn_slot 可用
        self.page_slot = uic.loadUi("重载机器人/槽铣界面.ui")   # 这里 page_slot.btn_back 可用

        # 加入堆栈
        self.stack.addWidget(self.page_main)  # index 0
        self.stack.addWidget(self.page_slot)  # index 1

        # 直接访问属性（uic.loadUi 会把 objectName 变成员属性）
        self.page_main.btn_slot.clicked.connect(lambda: self.stack.setCurrentIndex(1))

        self.page_slot.btn_back.clicked.connect(lambda: self.stack.setCurrentIndex(0))
# 连接槽铣界面内部的“下一步”按钮
        self.page_slot.pushButton_8.clicked.connect(lambda: self.page_slot.tabWidget.setCurrentIndex(1)) # 第一步 -> 第二步
        self.page_slot.pushButton_12.clicked.connect(lambda: self.page_slot.tabWidget.setCurrentIndex(2)) # 第二步 -> 第三步
        self.page_slot.pushButton_18.clicked.connect(lambda: self.page_slot.tabWidget.setCurrentIndex(3)) # 第三步 -> 第四步
        self.page_slot.pushButton_23.clicked.connect(lambda: self.page_slot.tabWidget.setCurrentIndex(4)) # 第四步 -> 加工监控

        # 连接槽铣界面内部的“上一步”按钮
        self.page_slot.pushButton_13.clicked.connect(lambda: self.page_slot.tabWidget.setCurrentIndex(0)) # 第二步 -> 第一步
        self.page_slot.pushButton_17.clicked.connect(lambda: self.page_slot.tabWidget.setCurrentIndex(1)) # 第三步 -> 第二步
        self.page_slot.pushButton_19.clicked.connect(lambda: self.page_slot.tabWidget.setCurrentIndex(2)) # 第四步 -> 第三步
        self.page_slot.pushButton_24.clicked.connect(lambda: self.page_slot.tabWidget.setCurrentIndex(3)) # 加工监控 -> 第四步

        # 连接槽铣界面内部的“主界面”按钮
        self.page_slot.btn_back.clicked.connect(lambda: self.stack.setCurrentIndex(0)) # 第一步 -> 主界面 (已存在，但为了清晰再次列出)
        self.page_slot.btn_back111.clicked.connect(lambda: self.stack.setCurrentIndex(0)) # 第二步 -> 主界面
        self.page_slot.btn_back111_2.clicked.connect(lambda: self.stack.setCurrentIndex(0)) # 第三步 -> 主界面
        self.page_slot.btn_back111_3.clicked.connect(lambda: self.stack.setCurrentIndex(0)) # 第四步 -> 主界面
        self.page_slot.btn_back111_4.clicked.connect(lambda: self.stack.setCurrentIndex(0)) # 加工监控 -> 主界面
# 连接槽铣界面内部的“下一步”按钮
        self.page_slot.pushButton_8.clicked.connect(lambda: self.page_slot.tabWidget.setCurrentIndex(1)) # 第一步 -> 第二步
        self.page_slot.pushButton_12.clicked.connect(lambda: self.page_slot.tabWidget.setCurrentIndex(2)) # 第二步 -> 第三步
        self.page_slot.pushButton_18.clicked.connect(lambda: self.page_slot.tabWidget.setCurrentIndex(3)) # 第三步 -> 第四步
        self.page_slot.pushButton_23.clicked.connect(lambda: self.page_slot.tabWidget.setCurrentIndex(4)) # 第四步 -> 加工监控

        # 连接槽铣界面内部的“上一步”按钮
        self.page_slot.pushButton_13.clicked.connect(lambda: self.page_slot.tabWidget.setCurrentIndex(0)) # 第二步 -> 第一步
        self.page_slot.pushButton_17.clicked.connect(lambda: self.page_slot.tabWidget.setCurrentIndex(1)) # 第三步 -> 第二步
        self.page_slot.pushButton_19.clicked.connect(lambda: self.page_slot.tabWidget.setCurrentIndex(2)) # 第四步 -> 第三步
        self.page_slot.pushButton_24.clicked.connect(lambda: self.page_slot.tabWidget.setCurrentIndex(3)) # 加工监控 -> 第四步

        # 槽铣界面第二步中查看刀具库按钮
        self.page_slot.pushButton_14.clicked.connect(self.open_table_editor)
        self.page_slot.pushButton_16.clicked.connect(self.open_table_editor)
        # 访问其它控件，比如读取用户在 page_slot 中 le_program 中输入的文本：
        # text = self.page_slot.le_program.text()

        self.setup_button_styles()

    def setup_button_styles(self):
        deep_blue_style = """
            QPushButton {
                background-color: #00008B;
                color: white;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #0000CD;
            }
            QPushButton:pressed {
                background-color: #000066;
            }
        """
        green_style="""
            QPushButton {
                background-color: #00AF4F;
                color: white;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #98FB98;
            }
            QPushButton:pressed {
                background-color: #228B22;
            }
        """
        red_style="""
            QPushButton {
                background-color: #FF0000;
                color: white;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #FF3030;
            }
            QPushButton:pressed {
                background-color: #FF4040;
            }
            """
        
        # 主界面按钮
        self.page_slot.btn_back.setStyleSheet(deep_blue_style)
        self.page_slot.btn_back111.setStyleSheet(deep_blue_style)
        self.page_slot.btn_back111_2.setStyleSheet(deep_blue_style)
        self.page_slot.btn_back111_3.setStyleSheet(deep_blue_style)
        self.page_slot.btn_back111_4.setStyleSheet(deep_blue_style)
        self.page_slot.pushButton_3.setStyleSheet(deep_blue_style)

        # 机器人通讯和自动模式设置成绿色
        self.page_slot.pushButton.setStyleSheet(green_style)
        self.page_slot.pushButton_6.setStyleSheet(green_style)
        # 系统急停和报警消除设置成蓝色
        self.page_slot.pushButton_2.setStyleSheet(deep_blue_style)
        self.page_slot.pushButton_7.setStyleSheet(deep_blue_style)
        # 手动模式设置成红色
        self.page_slot.pushButton_5.setStyleSheet(red_style)

        # 上一步按钮
        self.page_slot.pushButton_13.setStyleSheet(deep_blue_style)
        self.page_slot.pushButton_17.setStyleSheet(deep_blue_style)
        self.page_slot.pushButton_19.setStyleSheet(deep_blue_style)
        self.page_slot.pushButton_24.setStyleSheet(deep_blue_style)

        # 下一步按钮
        self.page_slot.pushButton_8.setStyleSheet(deep_blue_style)
        self.page_slot.pushButton_12.setStyleSheet(deep_blue_style)
        self.page_slot.pushButton_18.setStyleSheet(deep_blue_style)
        self.page_slot.pushButton_23.setStyleSheet(deep_blue_style)
        # UI中存在但代码未连接的按钮
        getattr(self.page_slot, 'pushButton_20', QPushButton()).setStyleSheet(deep_blue_style)


        # 开始加工按钮
        self.page_slot.pushButton_9.setStyleSheet(deep_blue_style)
        self.page_slot.pushButton_10.setStyleSheet(deep_blue_style)
        self.page_slot.pushButton_11.setStyleSheet(deep_blue_style)
        # UI中存在但代码未连接的按钮
        getattr(self.page_slot, 'pushButton_21', QPushButton()).setStyleSheet(deep_blue_style)
        getattr(self.page_slot, 'pushButton_22', QPushButton()).setStyleSheet(deep_blue_style)

    def open_table_editor(self):
        self.table_editor = TableEditorWindow()
        self.table_editor.show()

if __name__ == "__main__":
    QtCore.QCoreApplication.setAttribute(QtCore.Qt.AA_EnableHighDpiScaling)
    app = QApplication(sys.argv)
    app.setStyle("Fusion")
    w = MainWindow()
    w.show()
    sys.exit(app.exec_())
