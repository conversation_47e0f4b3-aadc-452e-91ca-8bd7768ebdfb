import socket
import time
from scipy.spatial.transform import Rotation as R

class RobotClient:
    def __init__(self, ip: str, port: int, buffer_size: int = 1024):
        """初始化客户端，创建 socket 对象但不立即连接。"""
        self.ip = ip
        self.port = port
        self.buffer_size = buffer_size
        self.sock = None

    def connect(self):
        """建立到机器人控制器的 TCP 连接。"""
        self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)  # :contentReference[oaicite:0]{index=0}
        try:
            self.sock.connect((self.ip, self.port))
            return 1
        except:
            return 0

    def close(self):
        """关闭当前连接。"""
        if self.sock:
            self.sock.close()
            print("Connection closed.")

    def send_command(self, cmd: str) -> str:
        """发送指令并等待返回字符串响应。"""
        if not self.sock:
            raise RuntimeError("Socket is not connected.")
        self.sock.sendall(cmd.encode('utf-8'))                                # :contentReference[oaicite:1]{index=1}
        print(f"Sent: {cmd}")
        data = self.sock.recv(self.buffer_size)
        if not data:
            raise ConnectionError("Connection closed by server.")
        resp = data.decode('utf-8')
        print(f"Received: {resp}")
        return resp

    @staticmethod
    def to_euler(data_str: str):
        """将 'x,y,z,rw,rx,ry,rz' 字符串转换为 [x,y,z,roll,pitch,yaw]。"""
        vals = list(map(float, data_str.split(',')))
        x, y, z, rw, rx, ry, rz = vals
        qt = [rx if abs(rx)>=1e-4 else 0,
              ry if abs(ry)>=1e-4 else 0,
              rz if abs(rz)>=1e-4 else 0,
              rw if abs(rw)>=1e-4 else 0]
        r = R.from_quat(qt)
        roll, pitch, yaw = r.as_euler('xyz', degrees=True)
        return [x, y, z, roll, pitch, yaw]                                 # :contentReference[oaicite:2]{index=2}

    @staticmethod
    def to_quaternion(data_str: str) -> str:
        """将 'x,y,z,roll,pitch,yaw' 字符串转换为 'x,y,z,w, qx,qy,qz' 格式字符串。"""
        x, y, z, roll, pitch, yaw = map(float, data_str.split(','))
        r = R.from_euler('xyz', [roll, pitch, yaw], degrees=True)
        qx, qy, qz, qw = r.as_quat()
        return f"{x},{y},{z},{qw},{qx},{qy},{qz}"

    def get_current_position(self):
        """发送 curpos 指令，返回 [x,y,z,roll,pitch,yaw]。"""
        resp = self.send_command("curpos")
        return self.to_euler(resp)

    def move_to_pose(self, velocity: int, cnt: int, pose_str: str):
        """构造并发送 movep 指令，pose_str 格式 'x,y,z,roll,pitch,yaw'。"""
        # 构造：movep:VEL:cnt: + quaternion-format-pose
        header = f"movep:{velocity:03d}:{cnt}:"
        quat_pose = self.to_quaternion(pose_str)
        full_cmd = header + quat_pose
        return self.send_command(full_cmd)

    def drill(self, velocity: int, tool: str, wobj: str, pose_str: str):
        """简易 drill:50:tool0:wobj1;X,Y,Z,roll,pitch,yaw; 单点钻孔。"""
        cmd = f"drill:{velocity:03d}:{tool}:{wobj};{pose_str};"
        return self.send_command(cmd)


if __name__ == "__main__":
    client = RobotClient("127.0.0.1", 55000)
    client.connect()


    # Drill：在 (150,0,0,0,0,0) 位置钻孔
    resp = client.send_command("drill:50:tool0:wobj1;0.0,0,0,1,0,0,0;")
    print(resp)

    client.close()
