import sys
import threading
from pymodbus.client import ModbusSerialClient
import struct
import time

from PyQt5.QtCore import QThread, pyqtSignal, Qt
from PyQt5.QtWidgets import (QApplication, QWidget, QLabel,
                             QVBoxLayout, QHBoxLayout, QPushButton)

class SimpleModbusSensorReader:
    def __init__(self, client: ModbusSerialClient, slave_address, register_address, lock: threading.Lock = None):
        self.client = client
        self.slave_address = slave_address
        self.register_address = register_address
        self.lock = lock

    def read_sensor(self):
        if self.lock:
            with self.lock:
                return self._read_once()
        else:
            return self._read_once()

    def _read_once(self):
        response = self.client.read_input_registers(
            self.register_address, count=2, slave=self.slave_address)
        regs = response.registers
        data = struct.pack('>HH', regs[0], regs[1])
        return struct.unpack('>f', data)[0]

class PollWorker(QThread):
    data_ready = pyqtSignal(dict)

    def __init__(self, port, slave_addresses, register_address,
                 baudrate=38400, parity='N', timeout=1.0, delay=1.0):
        super().__init__()
        self.client = ModbusSerialClient(port=port, baudrate=baudrate,
                                         parity=parity, timeout=timeout)
        self.client.connect()
        self.lock = threading.Lock()
        self.readers = [SimpleModbusSensorReader(self.client, sid, register_address, self.lock)
                        for sid in slave_addresses]
        self.delay = delay
        self._running = True

    def run(self):
        while self._running:
            results = {}
            for r in self.readers:
                try:
                    val = r.read_sensor()
                except Exception:
                    val = None
                results[r.slave_address] = val
            self.data_ready.emit(results)
            time.sleep(self.delay)

    def stop(self):
        self._running = False
        self.wait()
        self.client.close()

class SensorDashboard(QWidget):
    def __init__(self, port, slave_addresses, register_address):
        super().__init__()
        self.setWindowTitle("Modbus Sensor Dashboard")
        self.layout = QVBoxLayout()



        # Labels for each sensor
        self.labels = {}
        for sid in slave_addresses:
            hbox = QHBoxLayout()
            lbl_id = QLabel(f"传感器 {sid}:")
            lbl_id.setFixedWidth(80)
            lbl_val = QLabel("--")
            lbl_val.setAlignment(Qt.AlignRight)
            hbox.addWidget(lbl_id)
            hbox.addWidget(lbl_val)
            self.layout.addLayout(hbox)
            self.labels[sid] = lbl_val

        # Start/Stop buttons
        btn_layout = QHBoxLayout()
        self.start_btn = QPushButton("开始")
        self.stop_btn = QPushButton("停止")
        self.stop_btn.setEnabled(False)
        btn_layout.addWidget(self.start_btn)
        btn_layout.addWidget(self.stop_btn)
        self.layout.addLayout(btn_layout)

        self.setLayout(self.layout)

        # Worker thread
        self.worker = PollWorker(port, slave_addresses, register_address)
        self.worker.data_ready.connect(self.update_display)

        # Connect buttons
        self.start_btn.clicked.connect(self.start_polling)
        self.stop_btn.clicked.connect(self.stop_polling)

    def start_polling(self):
        self.worker.start()
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)

    def stop_polling(self):
        self.worker.stop()
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)

    def update_display(self, data: dict):
        for sid, val in data.items():
            if val is None:
                self.labels[sid].setText("Err")
            else:
                self.labels[sid].setText(f"{val:.2f}")

    def closeEvent(self, event):
        self.worker.stop()
        event.accept()

if __name__ == '__main__':
    app = QApplication(sys.argv)
    # 修改端口、从机地址和寄存器地址为实际值
    port = 'COM11'
    slave_ids = [1, 2, 3, 4]
    reg_addr = 0x18
    dashboard = SensorDashboard(port, slave_ids, reg_addr)
    dashboard.resize(300, 200)
    dashboard.show()
    sys.exit(app.exec_())
