import sys, math, copy
import numpy as np  # 导入numpy库并重命名为np
from PyQt5.QtCore import pyqtSignal, QThread, Qt
from PyQt5.QtWidgets import QMainWindow, QVBoxLayout, QPushButton, QFileDialog, QWidget, QHBoxLayout, QApplication, \
    QDialog
from PyQt5.QtOpenGL import QGLWidget
from OpenGL.GL import *
from OpenGL.GLU import *
import dxfgrabber
from PyQt5 import uic
from typing import List
from PyQt5.QtGui import QStandardItemModel, QStandardItem
from PyQt5.uic import loadUi
import win32com.client
import pythoncom
from datetime import datetime
from ga import Ga
from table_editor import TableEditorWindow

class Entity:  # DXF实体
    def __init__(self, dxftype, **kwargs):
        self.dxftype = dxftype
        self.parameters = {}
        for key, value in kwargs.items():
            rounded_value = round(value, 2) if isinstance(value, float) else value  # 对浮点数四舍五入到两位小数
            # 对0进行处理
            if rounded_value == -0.0:
                rounded_value = 0.0
            elif rounded_value == -0.00:
                rounded_value = 0.0
            elif rounded_value == -0:
                rounded_value = 0
            self.parameters[key] = rounded_value

# 读取dxf文件并提取信息
def get_dxf_entities(a,b):
    entities1 = []
    entities2 = []
    entity = Entity('LINE', layer=1, start_x=0.0, start_y=0.0, start_z=0.0, end_x=a,
                    end_y=0.0, end_z=0.0)
    entities1.append(entity)
    entity = Entity('LINE', layer=1, start_x=a, start_y=0.0, start_z=0.0, end_x=a,
                    end_y=b, end_z=0.0)
    entities1.append(entity)
    entity = Entity('LINE', layer=1, start_x=a, start_y=b, start_z=0.0, end_x=0.0,
                    end_y=b, end_z=0.0)
    entities1.append(entity)
    entity = Entity('LINE', layer=1, start_x=0.0, start_y=b, start_z=0.0, end_x=0.0,
                    end_y=0.0, end_z=0.0)
    entities1.append(entity)

    return entities1, entities2

# dxf排序
def arrange(entities, tolerance):
    Entities = copy.deepcopy(entities)
    for entity in Entities:
        entity.parameters.update({'found_adjacent': True})
    now = []
    sort = []
    found_adjacent = False
    while len(Entities) > 0:
        if not found_adjacent:
            found_adjacent = False
            entity = Entities[0]
            Type = entity.dxftype
            if Type == 'LINE':
                start_x = entity.parameters['start_x']
                start_y = entity.parameters['start_y']
                end_x = entity.parameters['end_x']
                end_y = entity.parameters['end_y']
                sort.append(entity)
                Entities.remove(entity)  # 生成G代码后删除当前实体
                for next_entity in Entities[0:]:
                    if next_entity.dxftype == 'LINE':
                        next_start_x = next_entity.parameters['start_x']
                        next_start_y = next_entity.parameters['start_y']
                        next_end_x = next_entity.parameters['end_x']
                        next_end_y = next_entity.parameters['end_y']
                        if (math.sqrt((next_start_x - end_x) ** 2 + (next_start_y - end_y) ** 2)) <= tolerance:
                            now = [next_entity]
                            sort.append(next_entity)
                            Entities.remove(next_entity)  # 生成G代码后删除当前实体
                            found_adjacent = True
                            break
                        elif (math.sqrt((next_end_x - end_x) ** 2 + (next_end_y - end_y) ** 2)) <= tolerance:
                            next_entity.parameters.update(
                                {'start_x': next_end_x, 'start_y': next_end_y, 'end_x': next_start_x,
                                 'end_y': next_start_y})
                            now = [next_entity]
                            sort.append(next_entity)
                            Entities.remove(next_entity)  # 生成G代码后删除当前实体
                            found_adjacent = True
                            break
                    elif next_entity.dxftype == 'ARC':
                        next_center_x = next_entity.parameters['center_x']
                        next_center_y = next_entity.parameters['center_y']
                        next_radius = next_entity.parameters['radius']
                        next_start_angle = next_entity.parameters['start_angle']
                        next_end_angle = next_entity.parameters['end_angle']
                        next_start_x = next_center_x + next_radius * np.cos(np.radians(next_start_angle))
                        next_start_y = next_center_y + next_radius * np.sin(np.radians(next_start_angle))
                        next_end_x = next_center_x + next_radius * np.cos(np.radians(next_end_angle))
                        next_end_y = next_center_y + next_radius * np.sin(np.radians(next_end_angle))
                        next_start_x = round(next_start_x, 2)
                        next_start_y = round(next_start_y, 2)
                        next_end_x = round(next_end_x, 2)
                        next_end_y = round(next_end_y, 2)
                        if (math.sqrt((next_start_x - end_x) ** 2 + (next_start_y - end_y) ** 2)) <= tolerance:
                            if 360 > (next_end_angle - next_start_angle) > 180:
                                next_radius = -next_radius
                            now = [next_entity]
                            sort.append(next_entity)
                            Entities.remove(next_entity)  # 生成G代码后删除当前实体
                            found_adjacent = True
                            break
                        elif (math.sqrt((next_end_x - end_x) ** 2 + (next_end_y - end_y) ** 2)) <= tolerance:
                            if 360 > (next_end_angle - next_start_angle) > 180:
                                next_radius = -next_radius
                            next_entity.parameters.update(
                                {'start_angle': next_end_angle, 'end_angle': next_start_angle})
                            now = [next_entity]
                            sort.append(next_entity)
                            Entities.remove(next_entity)  # 生成G代码后删除当前实体
                            found_adjacent = True
                            break
                if not found_adjacent:
                    sort[-1].parameters.update({'found_adjacent': False})
            elif Type == 'ARC':
                # 处理圆弧实体
                center_x = entity.parameters['center_x']
                center_y = entity.parameters['center_y']
                radius = entity.parameters['radius']
                start_angle = entity.parameters['start_angle']
                end_angle = entity.parameters['end_angle']
                start_x = center_x + radius * np.cos(np.radians(start_angle))
                start_y = center_y + radius * np.sin(np.radians(start_angle))
                end_x = center_x + radius * np.cos(np.radians(end_angle))
                end_y = center_y + radius * np.sin(np.radians(end_angle))
                start_x = round(start_x, 2)
                start_y = round(start_y, 2)
                end_x = round(end_x, 2)
                end_y = round(end_y, 2)
                if 360 > (end_angle - start_angle) > 180:
                    radius = -radius
                sort.append(entity)
                Entities.remove(entity)  # 生成G代码后删除当前实体
                for next_entity in Entities[0:]:
                    if next_entity.dxftype == 'LINE':
                        next_start_x = next_entity.parameters['start_x']
                        next_start_y = next_entity.parameters['start_y']
                        next_end_x = next_entity.parameters['end_x']
                        next_end_y = next_entity.parameters['end_y']
                        next_start_x = round(next_start_x, 2)
                        next_start_y = round(next_start_y, 2)
                        next_end_x = round(next_end_x, 2)
                        next_end_y = round(next_end_y, 2)
                        if (math.sqrt((next_start_x - end_x) ** 2 + (next_start_y - end_y) ** 2)) <= tolerance:
                            now = [next_entity]
                            sort.append(next_entity)
                            Entities.remove(next_entity)  # 生成G代码后删除当前实体
                            found_adjacent = True
                            break
                        elif (math.sqrt((next_end_x - end_x) ** 2 + (next_end_y - end_y) ** 2)) <= tolerance:
                            next_entity.parameters.update(
                                {'start_x': next_end_x, 'start_y': next_end_y, 'end_x': next_start_x,
                                 'end_y': next_start_y})
                            now = [next_entity]
                            sort.append(next_entity)
                            Entities.remove(next_entity)  # 生成G代码后删除当前实体
                            found_adjacent = True
                            break
                    elif next_entity.dxftype == 'ARC':
                        next_center_x = next_entity.parameters['center_x']
                        next_center_y = next_entity.parameters['center_y']
                        next_radius = next_entity.parameters['radius']
                        next_start_angle = next_entity.parameters['start_angle']
                        next_end_angle = next_entity.parameters['end_angle']
                        next_start_x = next_center_x + next_radius * np.cos(np.radians(next_start_angle))
                        next_start_y = next_center_y + next_radius * np.sin(np.radians(next_start_angle))
                        next_end_x = next_center_x + next_radius * np.cos(np.radians(next_end_angle))
                        next_end_y = next_center_y + next_radius * np.sin(np.radians(next_end_angle))
                        next_start_x = round(next_start_x, 2)
                        next_start_y = round(next_start_y, 2)
                        next_end_x = round(next_end_x, 2)
                        next_end_y = round(next_end_y, 2)
                        if (math.sqrt((next_start_x - end_x) ** 2 + (next_start_y - end_y) ** 2)) <= tolerance:
                            if 360 > (next_end_angle - next_start_angle) > 180:
                                next_radius = -next_radius
                            now = [next_entity]
                            sort.append(next_entity)
                            Entities.remove(next_entity)  # 生成G代码后删除当前实体
                            found_adjacent = True
                            break
                        elif (math.sqrt((next_end_x - end_x) ** 2 + (next_end_y - end_y) ** 2)) <= tolerance:
                            if 360 > (next_end_angle - next_start_angle) > 180:
                                next_radius = -next_radius
                            next_entity.parameters.update(
                                {'start_angle': next_end_angle, 'end_angle': next_start_angle})
                            now = [next_entity]
                            sort.append(next_entity)
                            Entities.remove(next_entity)  # 生成G代码后删除当前实体
                            found_adjacent = True
                            break
                if not found_adjacent:
                    sort[-1].parameters.update({'found_adjacent': False})
        elif found_adjacent:
            found_adjacent = False
            entity = now[0]
            Type = entity.dxftype
            if Type == 'LINE':
                start_x = entity.parameters['start_x']
                start_y = entity.parameters['start_y']
                end_x = entity.parameters['end_x']
                end_y = entity.parameters['end_y']
                for next_entity in Entities[0:]:
                    if next_entity.dxftype == 'LINE':
                        next_start_x = next_entity.parameters['start_x']
                        next_start_y = next_entity.parameters['start_y']
                        next_end_x = next_entity.parameters['end_x']
                        next_end_y = next_entity.parameters['end_y']
                        if (math.sqrt((next_start_x - end_x) ** 2 + (next_start_y - end_y) ** 2)) <= tolerance:
                            now = [next_entity]
                            sort.append(next_entity)
                            Entities.remove(next_entity)  # 生成G代码后删除当前实体
                            found_adjacent = True
                            break
                        elif (math.sqrt((next_end_x - end_x) ** 2 + (next_end_y - end_y) ** 2)) <= tolerance:
                            next_entity.parameters.update(
                                {'start_x': next_end_x, 'start_y': next_end_y, 'end_x': next_start_x,
                                 'end_y': next_start_y})
                            now = [next_entity]
                            sort.append(next_entity)
                            Entities.remove(next_entity)  # 生成G代码后删除当前实体
                            found_adjacent = True
                            break
                    elif next_entity.dxftype == 'ARC':
                        next_center_x = next_entity.parameters['center_x']
                        next_center_y = next_entity.parameters['center_y']
                        next_radius = next_entity.parameters['radius']
                        next_start_angle = next_entity.parameters['start_angle']
                        next_end_angle = next_entity.parameters['end_angle']
                        next_start_x = next_center_x + next_radius * np.cos(np.radians(next_start_angle))
                        next_start_y = next_center_y + next_radius * np.sin(np.radians(next_start_angle))
                        next_end_x = next_center_x + next_radius * np.cos(np.radians(next_end_angle))
                        next_end_y = next_center_y + next_radius * np.sin(np.radians(next_end_angle))
                        next_start_x = round(next_start_x, 2)
                        next_start_y = round(next_start_y, 2)
                        next_end_x = round(next_end_x, 2)
                        next_end_y = round(next_end_y, 2)
                        if (math.sqrt((next_start_x - end_x) ** 2 + (next_start_y - end_y) ** 2)) <= tolerance:
                            if 360 > (next_end_angle - next_start_angle) > 180:
                                next_radius = -next_radius
                            now = [next_entity]
                            sort.append(next_entity)
                            Entities.remove(next_entity)  # 生成G代码后删除当前实体
                            found_adjacent = True
                            break
                        elif (math.sqrt((next_end_x - end_x) ** 2 + (next_end_y - end_y) ** 2)) <= tolerance:
                            if 360 > (next_end_angle - next_start_angle) > 180:
                                next_radius = -next_radius
                            next_entity.parameters.update(
                                {'start_angle': next_end_angle, 'end_angle': next_start_angle})
                            now = [next_entity]
                            sort.append(next_entity)
                            Entities.remove(next_entity)  # 生成G代码后删除当前实体
                            found_adjacent = True
                            break
                if not found_adjacent:
                    sort[-1].parameters.update({'found_adjacent': False})
            elif Type == 'ARC':
                center_x = entity.parameters['center_x']
                center_y = entity.parameters['center_y']
                radius = entity.parameters['radius']
                start_angle = entity.parameters['start_angle']
                end_angle = entity.parameters['end_angle']
                start_x = center_x + radius * np.cos(np.radians(start_angle))
                start_y = center_y + radius * np.sin(np.radians(start_angle))
                end_x = center_x + radius * np.cos(np.radians(end_angle))
                end_y = center_y + radius * np.sin(np.radians(end_angle))
                start_x = round(start_x, 2)
                start_y = round(start_y, 2)
                end_x = round(end_x, 2)
                end_y = round(end_y, 2)
                for next_entity in Entities[0:]:
                    if next_entity.dxftype == 'LINE':
                        next_start_x = next_entity.parameters['start_x']
                        next_start_y = next_entity.parameters['start_y']
                        next_end_x = next_entity.parameters['end_x']
                        next_end_y = next_entity.parameters['end_y']
                        if (math.sqrt((next_start_x - end_x) ** 2 + (next_start_y - end_y) ** 2)) <= tolerance:
                            now = [next_entity]
                            sort.append(next_entity)
                            Entities.remove(next_entity)  # 生成G代码后删除当前实体
                            found_adjacent = True
                            break
                        elif (math.sqrt((next_end_x - end_x) ** 2 + (next_end_y - end_y) ** 2)) <= tolerance:
                            next_entity.parameters.update(
                                {'start_x': next_end_x, 'start_y': next_end_y, 'end_x': next_start_x,
                                 'end_y': next_start_y})
                            now = [next_entity]
                            sort.append(next_entity)
                            Entities.remove(next_entity)  # 生成G代码后删除当前实体
                            found_adjacent = True
                            break
                    elif next_entity.dxftype == 'ARC':
                        next_center_x = next_entity.parameters['center_x']
                        next_center_y = next_entity.parameters['center_y']
                        next_radius = next_entity.parameters['radius']
                        next_start_angle = next_entity.parameters['start_angle']
                        next_end_angle = next_entity.parameters['end_angle']
                        next_start_x = next_center_x + next_radius * np.cos(np.radians(next_start_angle))
                        next_start_y = next_center_y + next_radius * np.sin(np.radians(next_start_angle))
                        next_end_x = next_center_x + next_radius * np.cos(np.radians(next_end_angle))
                        next_end_y = next_center_y + next_radius * np.sin(np.radians(next_end_angle))
                        next_start_x = round(next_start_x, 2)
                        next_start_y = round(next_start_y, 2)
                        next_end_x = round(next_end_x, 2)
                        next_end_y = round(next_end_y, 2)
                        if (math.sqrt((next_start_x - end_x) ** 2 + (next_start_y - end_y) ** 2)) <= tolerance:
                            if 360 > (next_end_angle - next_start_angle) > 180:
                                next_radius = -next_radius
                            now = [next_entity]
                            sort.append(next_entity)
                            Entities.remove(next_entity)  # 生成G代码后删除当前实体
                            found_adjacent = True
                            break
                        elif (math.sqrt((next_end_x - end_x) ** 2 + (next_end_y - end_y) ** 2)) <= tolerance:
                            if 360 > (next_end_angle - next_start_angle) > 180:
                                next_radius = -next_radius
                            next_entity.parameters.update(
                                {'start_angle': next_end_angle, 'end_angle': next_start_angle})
                            now = [next_entity]
                            sort.append(next_entity)
                            Entities.remove(next_entity)  # 生成G代码后删除当前实体
                            found_adjacent = True
                            break
                if not found_adjacent:
                    sort[-1].parameters.update({'found_adjacent': False})

    sort[-1].parameters.update({'found_adjacent': False})
    return sort
    # return connects

# 划分不同封闭图形
def split_entities(entities):
    result = []  # 初始化结果列表，用于存储拆分后的实体子列表
    current_list = []  # 初始化当前列表，用于暂存连续的实体

    for entity in entities:  # 遍历所有实体
        current_list.append(entity)  # 将当前实体添加到当前列表
        if not entity.parameters['found_adjacent']:  # 如果当前实体没有相邻标记
            result.append(current_list)  # 将当前列表添加到结果列表中
            current_list = []  # 重置当前列表以开始新的子列表

    if current_list:  # 如果当前列表中还有剩余实体
        result.append(current_list)  # 将它们添加到结果列表中

    return result  # 返回拆分后的结果列表

def get_point(entities, tool_offset, total_depth, depth,out):
    layer = []  # 最终返回的所有points的列表
    # 遍历 entities 中每个子列表
    for sublist in entities:
        # 计算当前子列表中所有 entity 的起点坐标
        entities_points = []
        for entity in sublist:
            start_x = entity.parameters['start_x']
            start_y = entity.parameters['start_y']
            entities_points.append((start_x, start_y))
        if not entities_points:
            continue

        # 取最小值点和最大值点（先按 x，然后 x 相同时 y 小的优先）
        point1 = min(entities_points, key=lambda p: (p[0], p[1]))
        point2 = max(entities_points, key=lambda p: (p[0], p[1]))

        # 起始点和终点，根据tool_offset平移
        start_point = (point1[0] - out , point1[1] , 0.0)
        end_point = (point2[0] + out, point2[1])

        # 计算水平方向步数和余量
        num = (end_point[1] - start_point[1]) // (tool_offset)
        # last = (end_point[1] - start_point[1]) % (tool_offset)
        # if last != 0:
        #     num = num + 1

        # 计算深度层数
        depth_num = total_depth // depth
        depth_last = total_depth % depth

        # 按深度生成各层的点
        j = 0
        while j < depth_num:
            i = 0
            x = start_point[0]
            y = start_point[1]
            z = 0.0
            points = []
            points.append((x, y, z))
            while i < num:
                # 横向平移
                x = x + (end_point[0] - start_point[0]) * ((-1) ** i)
                points.append((x, y, z))
                # 纵向平移
                y = y + tool_offset
                points.append((x, y, z))
                i += 1
            # 最后一次横向平移
            x = x + (end_point[0] - start_point[0]) * ((-1) ** i)
            points.append((x, y, z))
            # 处理最后不足一个步长的情况
            # if last != 0:
            #     y = y + last
            #     points.append((x, y, z))
            #     x = x + (end_point[0] - start_point[0]) * ((-1) ** (i + 1))
            #     points.append((x, y, z))
            j += 1
            layer.append(points)

        # 如果剩余深度不足depth，则单独生成一层
        if depth_last != 0:
            i = 0
            x = start_point[0]
            y = start_point[1]
            points = []
            while i < num:
                x = x + (end_point[0] - start_point[0]) * ((-1) ** i)
                points.append((x, y, total_depth))
                y = y + 0.5 * tool_offset
                points.append((x, y, total_depth))
                i += 1
            x = x + (end_point[0] - start_point[0]) * ((-1) ** i)
            points.append((x, y, total_depth))
            # if last != 0:
            #     y = y + last
            #     points.append((x, y, total_depth))
            #     x = x + (end_point[0] - start_point[0]) * ((-1) ** (i + 1))
            #     points.append((x, y, total_depth))
            layer.append(points)
    return layer

def create_line_entities(layer_list):
    """
    根据 layer_list 中每个子列表的有序点坐标，
    为每个子列表生成相邻点之间的线实体，并将生成的线实体
    保存在一个子列表中，最后返回一个包含所有子列表的列表。

    参数:
        layer_list: 一个列表，每个子列表内存储了多个点的坐标，
                    点的格式为 (x, y, z)

    返回:
        一个列表，其中每个元素都是一个子列表，子列表中包含对应层的所有线实体（Entity 对象）。
    """
    # 最终存储所有层中直线实体的列表
    line_layers = []

    # 遍历 layer_list 中的每一层（子列表）
    for layer in layer_list:
        # 在当前层中生成直线实体的列表
        line_entities = []
        for i in range(len(layer) - 1):
            start_point = layer[i]
            end_point = layer[i + 1]
            # 解包坐标值
            start_x, start_y, start_z = start_point
            end_x, end_y, end_z = end_point
            # 构造一个直线实体
            entity = Entity('LINE',
                            layer=layer,  # 这里也可传入其它层的标识，比如索引
                            start_x=start_x, start_y=start_y, start_z=start_z,
                            end_x=end_x, end_y=end_y, end_z=end_z)
            line_entities.append([entity])
        # # 将当前层生成的直线实体列表添加到 line_layers 中
        # line_layers.append(line_entities)

    return line_entities

def get_gcode(point_list,depth):
    global gcode, line_number
    HangHao = "N"
    line_number = 0
    time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    gcode += f"INI\n"
    gcode += f"SPTP HOME Vel=100% DEFAULT\n"
    # gcode += f"BASE_DATA[1]={{X 0, Y 0, Z 0, A 0, B 0, C 0}}\n"
    gcode += f"PTP{{a4 -180}}C_PTP\n"
    gcode += f"wait sec 2\n"
    for i, sublist in enumerate(point_list):
        start_point = sublist[0]
        start_x = start_point[0]
        start_y = start_point[1]
        start_z = -1*depth
        gcode += f"LIN {{X {start_x},Y {start_y},Z {10}}}\n"
        gcode += f"LIN {{X {start_x},Y {start_y},Z {start_z}}}\n"
        # gcode += f"LIN z {{{start_z}}} #BASE\n"
        for ponint in sublist[1:]:
            gcode += f"LIN {{X {ponint[0]},Y {ponint[1]},Z {start_z}}}\n"

        # 检查是否存在下一个子列表
        if i + 1 < len(point_list):
            next_sublist = point_list[i + 1]
            next_start_point = next_sublist[0]
            next_start_x = next_start_point[0]
            next_start_y = next_start_point[1]
            if start_x == next_start_x and start_y == next_start_y:
                line_number += 10
                gcode += f"LIN z {start_z + 2} #BASE\n"
            else:
                line_number += 10
                gcode += f"{HangHao}{line_number} LIN z{10} #BASE\n"
        else:
            line_number += 10
            start_x = sublist[-1][0]
            start_y = sublist[-1][1]
            gcode += f"LIN {{X {start_x},Y {start_y},Z {10}}}\n"
            line_number += 10
            gcode += f"LIN {{X {0.0},Y {0.0},z {10} #BASE\n"
    gcode += f"SPTP HOME Vel=100% DEFAULT\n"
    gcode += f"END\n"

    return gcode

def get_gcode2(main_list):
    global gcode, line_number
    x = 0.0
    y = 0.0
    line_number = 0
    time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    line_number += 10
    # gcode += f" (生成于{time}}})\n"
    gcode += f"INI\n"
    gcode += f"SPTP HOME Vel=100% DEFAULT\n"
    # gcode += f"BASE_DATA[1]={{X 0, Y 0, Z 0, A 0, B 0, C 0}}\n"
    gcode += f"PTP{{a4 -180}}C_PTP\n"
    gcode += f"wait sec 2\n"
    for i, sublist in enumerate(main_list):
        entity = sublist[0]
        if 'type' in entity.parameters and entity.parameters['type'] == 'connect':
            entity = sublist[1]
            start_x = entity.parameters['start_x'] - x
            start_y = entity.parameters['start_y'] - y
            start_z = sublist[0].parameters.get('end_z', 0.0)  # 默认值为0.0，如果没有指定z坐标
            end_x = entity.parameters['end_x'] - x
            end_y = entity.parameters['end_y'] - y  # 默认值为0.0，如果没有指定z坐标
            end_z = sublist[2].parameters.get('end_z', 0.0)
            line_number += 10
            gcode += f"LIN {{X {start_x},Y {start_y},Z {start_z}}}\n"
            line_number += 10
            gcode += f"LIN {{X {end_x},Y {end_y},Z {start_z}}}\n"
            line_number += 10
            gcode += f"LIN {{X {end_x},Y {end_y},Z {end_z}}}\n"
        else:
            for entity in sublist:
                if entity.dxftype == 'LINE':
                    start_x = entity.parameters['start_x'] - x
                    start_y = entity.parameters['start_y'] - y
                    start_z = entity.parameters.get('start_z', 0.0)  # 默认值为0.0，如果没有指定z坐标
                    end_x = entity.parameters['end_x'] - x
                    end_y = entity.parameters['end_y'] - y
                    end_z = entity.parameters.get('end_z', 0.0)# 默认值为0.0，如果没有指定z坐标
                    line_number += 10
                    gcode += f"LIN {{X {end_x},Y {end_y},Z {end_z}}}\n"
                # elif entity.dxftype == 'ARC':
                #     center_x = entity.parameters['center_x'] - x
                #     center_y = entity.parameters['center_y'] - y
                #     radius = entity.parameters['radius']
                #     start_angle = entity.parameters['start_angle']
                #     end_angle = entity.parameters['end_angle']
                #     start_x = center_x + radius * np.cos(np.radians(start_angle))
                #     start_y = center_y + radius * np.sin(np.radians(start_angle))
                #     end_x = center_x + radius * np.cos(np.radians(end_angle))
                #     end_y = center_y + radius * np.sin(np.radians(end_angle))
                #     center_x = round(center_x, 2)
                #     center_y = round(center_y, 2)
                #     start_x = round(start_x, 2)
                #     start_y = round(start_y, 2)
                #     end_x = round(end_x, 2)
                #     end_y = round(end_y, 2)
                #
                #     # 计算圆心的相对偏移量
                #     I = center_x - start_x  # 圆心X相对于起点X的偏移
                #     I = round(I, 2)
                #     J = center_y - start_y  # 圆心Y相对于起点Y的偏移
                #     J = round(J, 2)
                #
                #     # 判断圆弧的方向：如果起始角度大于结束角度，则顺时针；否则逆时针
                #     if start_angle > end_angle:
                #         gcode += f"{HangHao}{line_number} G02 X{end_x} Y{end_y} I{I} J{J} {'F' + str(feedspeed) if feedspeed != 0 else ''}\n"  # 顺时针圆弧
                #     else:
                #         gcode += f"{HangHao}{line_number} G03 X{end_x} Y{end_y} I{I} J{J} {'F' + str(feedspeed) if feedspeed != 0 else ''}\n"  # 逆时针圆弧
                #
                #     line_number += 10  # 增加行号

    gcode += f"SPTP HOME Vel=100% DEFAULT\n"
    gcode += f"END\n"
    return gcode

def collect_interpolated_toolpaths(highlighted_entities,ToolPath) -> list[list]:
    """
    1) 对相邻索引差==2的对，生成补全区间
    2) 合并重叠或相邻区间
    3) 添加没有任何补全区间覆盖的孤立索引作为单元素区间
    4) 按起始索引排序，映射回 ToolPath 子列表
    5) 扁平化每个子列表中的单元素列表，得到最终的值列表
    """
    # 1. 获取高亮组的索引（去重、排序）
    hi_idxs = sorted({
        ToolPath.index(g)
        for g in highlighted_entities
        if g in ToolPath
    })
    if not hi_idxs:
        return []

    # 2. 生成差==2的补全区间
    raw_ranges = []
    for a, b in zip(hi_idxs, hi_idxs[1:]):
        if abs(b - a) == 2:
            raw_ranges.append(tuple(sorted((a, b))))

    # 3. 合并重叠或相邻区间
    merged = []
    if raw_ranges:
        raw_ranges.sort(key=lambda x: x[0])
        merged = [list(raw_ranges[0])]
        for start, end in raw_ranges[1:]:
            last_start, last_end = merged[-1]
            if start <= last_end + 1:
                merged[-1][1] = max(last_end, end)
            else:
                merged.append([start, end])

    # 4. 找出孤立索引
    singleton = [(idx, idx) for idx in hi_idxs
                 if not any(s <= idx <= e for s, e in merged)]

    # 5. 合并所有区间并排序
    all_ranges = merged + singleton
    all_ranges.sort(key=lambda x: x[0])

    # 6. 映射回 ToolPath 子列表
    segments = [ToolPath[s:e+1] for s, e in all_ranges]

    # 7. 扁平化每个子列表中的单元素列表
    flattened = []
    for seg in segments:
        flat = [item for sub in seg for item in (sub if isinstance(sub, list) else [sub])]
        flattened.append(flat)
    return flattened

# 得到一段快移线的起点
def get_start(entity):
    if entity.dxftype == 'LINE':
        start_x = entity.parameters['end_x']
        start_y = entity.parameters['end_y']
    elif entity.dxftype == 'ARC':
        center_x = entity.parameters['center_x']
        center_y = entity.parameters['center_y']
        radius = entity.parameters['radius']
        start_angle = entity.parameters['start_angle']
        end_angle = entity.parameters['end_angle']
        start_x = center_x + radius * np.cos(np.radians(end_angle))
        start_y = center_y + radius * np.sin(np.radians(end_angle))
        start_x = round(start_x, 2)
        start_y = round(start_y, 2)
    return start_x, start_y

# 得到一段快移线的终点
def get_end(entity):
    if entity.dxftype == 'LINE':
        end_x = entity.parameters['start_x']
        end_y = entity.parameters['start_y']
    elif entity.dxftype == 'ARC':
        center_x = entity.parameters['center_x']
        center_y = entity.parameters['center_y']
        radius = entity.parameters['radius']
        start_angle = entity.parameters['start_angle']
        end_angle = entity.parameters['end_angle']
        end_x = center_x + radius * np.cos(np.radians(start_angle))
        end_y = center_y + radius * np.sin(np.radians(start_angle))
        end_x = round(end_x, 2)
        end_y = round(end_y, 2)
    return end_x, end_y

def tool_path(main_list,depth):
    # 新的主列表，用于存储符合条件的子列表
    depth = -1*depth
    travel_height = 10.0
    for i in range(len(main_list) - 1, -1, -1):
        sublist = main_list[i]
        # 检查子列表的第一个 Entity 对象是否存在 'type' 键
        if 'type' in sublist[0].parameters:
            # 删除符合条件的子列表
            del main_list[i]
    first_path_list = []
    start_x = 0.0
    start_y = 0.0
    start_z = 0.0
    entity = main_list[0][0]
    end_x, end_y = get_end(entity)
    entity1 = Entity('LINE', type='connect', start_x=start_x, start_y=start_y, start_z=0.0, end_x=start_x,
                     end_y=start_y,
                     end_z=travel_height)
    entity2 = Entity('LINE', type='connect', start_x=start_x, start_y=start_y, start_z=travel_height, end_x=end_x,
                     end_y=end_y,
                     end_z=travel_height)
    entity3 = Entity('LINE', type='connect', start_x=end_x, start_y=end_y, start_z=travel_height, end_x=end_x,
                     end_y=end_y,
                     end_z=depth)
    first_path_list.append(entity1)
    first_path_list.append(entity2)
    first_path_list.append(entity3)
    main_list.insert(0, first_path_list)

    i = 1  # 初始化索引为1，因为第0位已被新插入的子列表占据
    while i < len(main_list):
        path_list = []
        # 获取当前子列表的和
        current_entity = main_list[i][-1]
        start_x, start_y = get_start(current_entity)
        # 获取下一个子列表的和，如果没有下一个则使用0
        if i + 1 < len(main_list):
            next_entity = main_list[i + 1][0]
            end_x, end_y = get_end(next_entity)
            end_Z = depth
        else:
            end_x = 0.0
            end_y = 0.0
            end_Z = 0.0

        entity1 = Entity('LINE', type='connect', start_x=start_x, start_y=start_y, start_z=depth, end_x=start_x,
                         end_y=start_y,
                         end_z=travel_height)
        entity2 = Entity('LINE', type='connect', start_x=start_x, start_y=start_y, start_z=travel_height, end_x=end_x,
                         end_y=end_y,
                         end_z=travel_height)
        entity3 = Entity('LINE', type='connect', start_x=end_x, start_y=end_y, start_z=travel_height, end_x=end_x,
                         end_y=end_y,
                         end_z=end_Z)
        path_list.append(entity1)
        path_list.append(entity2)
        path_list.append(entity3)

        # 插入到两个相邻子列表之间
        main_list.insert(i + 1, path_list)

        # 跳过当前子列表和新插入的子列表
        i += 2

class DXFViewer(QGLWidget):
    def __init__(self, parent=None):
        super(DXFViewer, self).__init__(parent)
        self.setMinimumSize(800, 600)  # 设置窗口的最小尺寸
        self.dxf_data = None  # 存储加载的DXF文件数据
        self.bounds = None  # 存储DXF文件的边界信息
        self.show_axes = False  # 标志位，决定是否显示坐标轴
        self.last_pos = None  # 存储鼠标的最后位置
        self.reset_view_params()  # 初始化视图参数
        self.highlighted_entities = []  # 新增：存储高亮实体列表
        self.Offsets = []
        self.OffsetsResults = []
        self.ToolPath = []
        self.is_2d_mode = True  # 新增：标志位，用于控制是否2D模式
        self.current_voice_thread = None
        self.origin_x = 0.0  # 原点的 X 坐标
        self.origin_y = 0.0  # 原点的 Y 坐标
        self.tolerance = 1.0

    def initializeGL(self):
        """OpenGL初始化"""
        glClearColor(1.0, 1.0, 1.0, 1.0)  # 设置背景颜色为白色
        glEnable(GL_DEPTH_TEST)  # 启用深度测试
        glEnable(GL_LINE_SMOOTH)  # 启用线条抗锯齿
        glHint(GL_LINE_SMOOTH_HINT, GL_NICEST)  # 设置线条抗锯齿的质量为最好
        glEnable(GL_BLEND)  # 启用混合
        glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA)  # 设置混合函数

    def resizeGL(self, w, h):
        """当窗口大小改变时，调整视口"""
        if h == 0:
            h = 1  # 防止高度为0
        glViewport(0, 0, w, h)  # 设置视口
        glMatrixMode(GL_PROJECTION)  # 设置矩阵模式为投影模式
        glLoadIdentity()
        # 调整近剪裁面和远剪裁面
        gluPerspective(45.0, w / h, 0.1, 5000.0)  # 设置透视投影
        glMatrixMode(GL_MODELVIEW)  # 返回模型视图模式

    def load_dxf(self, a,b):
        """加载DXF文件"""
        try:
            entities1, entities2 = get_dxf_entities(a,b)
            self.entities1 = entities1  # 更新实体坐标
            self.entities2 = entities2  # 更新实体坐标
            self.sort1 = []
            self.sort2 = []
            self.Offsets = []  # 偏移线列表
            self.OffsetsResults = []  # 确认的偏移线列表
            self.highlighted_entities = []  # 选中的红色高亮列表
            self.ToolPath = []
            self.show_axes = True
            entities = entities1 + entities2
            self.sort = arrange(entities, self.tolerance)
            self.result = split_entities(self.sort)  # 划分完封闭图形
            self.dxf_data = self.sort  # 读取DXF文件
            self.calculate_bounds()  # 计算DXF内容的边界
            self.reset_view_params()  # 重置视图参数
            self.update()  # 更新视图
        except Exception as e:
            print(f"Failed to load DXF file: {e}")

    def reset_view_params(self):
        """重置视图参数到默认值"""
        self.x_rot = 0
        self.y_rot = 0
        if not self.dxf_data:
            self.z_trans = -100
            self.center = [0, 0, 0]  # 设置视图的中心点
        else:
            self.z_trans = self.default_z_trans
            self.center = self.default_center  # 设置视图的中心点
        self.x_trans = 0
        self.y_trans = 0
        self.scale_factor = 1.0  # 设置缩放系数

    def set_2d_mode(self):
        """启用或禁用2D模式"""
        if self.is_2d_mode == True:
            self.reset_view_params()

    def calculate_bounds(self):
        """计算DXF文件边界"""
        if not self.dxf_data:
            return
        min_x, min_y, max_x, max_y = float('inf'), float('inf'), float('-inf'), float('-inf')
        for entity in self.dxf_data:
            if entity.dxftype == 'LINE':
                min_x = min(min_x, entity.parameters['start_x'], entity.parameters['end_x'])
                min_y = min(min_y, entity.parameters['start_y'], entity.parameters['end_y'])
                max_x = max(max_x, entity.parameters['start_x'], entity.parameters['end_x'])
                max_y = max(max_y, entity.parameters['start_y'], entity.parameters['end_y'])
            elif entity.dxftype == 'CIRCLE' or entity.dxftype == 'ARC':
                cx, cy, r = entity.parameters['center_x'], entity.parameters['center_y'], entity.parameters['radius']
                min_x = min(min_x, cx - r)
                min_y = min(min_y, cy - r)
                max_x = max(max_x, cx + r)
                max_y = max(max_y, cy + r)
        self.bounds = [min_x, max_x, min_y, max_y]  # 保存边界信息
        self.center = [(min_x + max_x) / 2, (min_y + max_y) / 2, 0]  # 计算中心点
        self.default_center = [(min_x + max_x) / 2, (min_y + max_y) / 2, 0]  # 计算中心点
        bounding_box_size = max(max_x - min_x, max_y - min_y)
        self.scale_factor = 100.0 / bounding_box_size if bounding_box_size else 1.0  # 设置缩放系数，避免除以零
        self.z_trans = -bounding_box_size * 1.5  # 移动视图以适应内容
        self.default_z_trans = -bounding_box_size * 1.5

    def update_view(self):
        """更新视图变换参数"""
        glTranslatef(self.x_trans, self.y_trans, self.z_trans)  # 平移变换
        glScalef(self.scale_factor, self.scale_factor, self.scale_factor)  # 缩放变换
        glRotatef(self.x_rot, 1.0, 0.0, 0.0)  # 绕X轴旋转
        glRotatef(self.y_rot, 0.0, 1.0, 0.0)  # 绕Y轴旋转
        glTranslatef(-self.center[0], -self.center[1], -self.center[2])  # 把视图中心移到原点

    def mousePressEvent(self, event):
        """鼠标按下事件处理"""
        self.last_pos = event.pos()  # 记录鼠标当前位置
        if event.button() == Qt.LeftButton:
            self.handle_click(event.pos())  # 处理鼠标左键点击

    def mouseMoveEvent(self, event):
        """处理鼠标移动事件"""
        dx = event.x() - self.last_pos.x()  # 计算鼠标X轴移动量
        dy = event.y() - self.last_pos.y()  # 计算鼠标Y轴移动量
        buttons = int(event.buttons())
        if buttons & Qt.LeftButton:
            if not self.is_2d_mode:  # 在3D模式下允许旋转
                self.x_rot += dy  # 鼠标左键按下时旋转视图
                self.y_rot += dx
        elif buttons & Qt.RightButton:
            self.x_trans += dx * 0.1  # 鼠标右键按下时平移视图
            self.y_trans -= dy * 0.1
        self.last_pos = event.pos()  # 更新鼠标位置
        self.update()

    def wheelEvent(self, event):
        """处理鼠标滚轮事件"""
        num_degrees = event.angleDelta().y()  # 获取滚轮滚动的角度
        self.z_trans = max(min(self.z_trans + num_degrees * 0.05, -0.1), -1000)  # 调整视图的平移量
        # print(self.z_trans)  # 打印平移量
        self.update()  # 更新视图

    def paintGL(self):
        """绘制OpenGL内容"""
        glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT)  # 清除颜色和深度缓存
        glLoadIdentity()
        self.update_view()  # 更新视图参数

        if self.show_axes:
            self.draw_axes_3d()  # 如果需要显示坐标轴则绘制坐标轴

        if hasattr(self, 'entities1') or hasattr(self, 'entities2'):  # 如果直线和圆弧坐标已存在

            self.draw_dxf(self.Offsets, [0, 0, 1], True)  # 如果有DXF文件数据则绘制DXF内容，蓝色绘制
            self.draw_dxf(self.OffsetsResults, [0, 0, 1], False)  # 如果有DXF文件数据则绘制DXF内容，蓝色绘制
            self.draw_dxf(self.ToolPath, [0, 1, 0,0.5], False,True)  # 绿色
            self.draw_dxf(split_entities(self.sort), [0, 0, 0], False)  # 如果有DXF文件数据则绘制DXF内容,黑色绘制
            self.draw_origin_marker(self.origin_x, self.origin_y)  # 调用绘制原点标识的方法
        glFlush()  # 强制刷新

    def draw_origin_marker(self, x=0.0, y=0.0):
        """
        绘制DXF图案原点（0, 0）的标识（包含圆形和十字，大小自适应缩放）。
        原点标志将根据给定的 (x, y) 坐标绘制。

        参数:
            x, y: 原点标志的位置（默认为 (0, 0)）。
        """
        glColor3f(1.0, 0.0, 0.0)  # 设置标识颜色，这里设为红色，可根据喜好调整
        glLineWidth(2.0)  # 设置线宽，使标识更明显，可按需修改
        marker_size = 5 * self.scale_factor  # 根据缩放因子调整标识整体大小

        # 绘制圆形
        num_segments = 50  # 圆形近似多边形的分段数
        circle_radius = marker_size  # 圆形半径根据整体标识大小确定
        glBegin(GL_LINE_LOOP)
        for i in range(num_segments):
            theta = 2.0 * math.pi * i / num_segments
            x_offset = circle_radius * math.cos(theta)
            y_offset = circle_radius * math.sin(theta)
            glVertex3f(x + x_offset, y + y_offset, 0)  # 根据给定坐标 (x, y) 偏移
        glEnd()

        # 绘制水平线段（十字的横）
        glBegin(GL_LINES)
        glVertex3f(x - marker_size, y, 0)  # 起点根据大小偏移
        glVertex3f(x + marker_size, y, 0)  # 终点根据大小偏移
        glEnd()

        # 绘制垂直线段（十字的竖）
        glBegin(GL_LINES)
        glVertex3f(x, y - marker_size, 0)  # 起点根据大小偏移
        glVertex3f(x, y + marker_size, 0)  # 终点根据大小偏移
        glEnd()

    def update_origin_marker(self, x, y):
        """
        更新原点坐标，并请求重新绘制 OpenGL。
        """
        self.origin_x = x
        self.origin_y = y
        self.update()  # 触发 OpenGL 控件重绘

    def draw_dxf(self, entities, rgb=None, dashed=False, draw_arrow=False):
            """绘制DXF文件中的实体"""
            for i, sublist in enumerate(entities):
                if rgb is None:
                    rgb = [0, 0, 0]  # 未设置颜色，默认黑色绘制
                for entity in sublist:
                    # 检查是否有透明度（Alpha），如果有则使用 glColor4f
                    if len(rgb) == 3:
                        glColor3f(*rgb)  # 不带透明度的颜色
                    elif len(rgb) == 4:
                        glColor4f(*rgb)  # 带透明度的颜色

                    # 如果类型参数存在，改为绿色
                    if 'type' in entity.parameters:
                        glColor3f(0, 1, 0)  # 显示颜色为绿色

                    # 如果是高亮实体，改为红色
                    for sublist0 in self.highlighted_entities:
                        if entity in sublist0:
                            glColor3f(1, 0, 0)  # 高亮显示颜色为红色
                    # 查询当前的线宽
                    line_width = glGetFloatv(GL_LINE_WIDTH)

                    # else:
                    #     glLineWidth(5.0)  # 默认为 5.0

                    # 根据参数决定是否启用虚线
                    if dashed:
                        glEnable(GL_LINE_STIPPLE)
                        glLineStipple(1, 0x00FF)  # 设置虚线模式，0x00FF 表示短划线
                    else:
                        glDisable(GL_LINE_STIPPLE)  # 禁用虚线模式以绘制实线

                    # 绘制直线实体
                    if entity.dxftype == 'LINE':
                        sx, sy, sz = entity.parameters['start_x'], entity.parameters['start_y'], entity.parameters.get(
                            'start_z', 0)
                        ex, ey, ez = entity.parameters['end_x'], entity.parameters['end_y'], entity.parameters.get(
                            'end_z', 0)
                        # —— 主 线 ——
                        # glColor3f(*rgb[:3])  # 用传入的颜色画主线
                        # 根据颜色调整线宽
                        if rgb == [0, 0, 0]:  # 如果是黑色
                            glLineWidth(5.0)
                        # glLineWidth(1.0)
                        glBegin(GL_LINES)
                        glVertex3f(sx, sy, sz)
                        glVertex3f(ex, ey, ez)
                        glEnd()
                        if draw_arrow:
                            # —— 箭 头 ——
                            # 可视化调试：先设置显眼颜色和粗细
                            glColor3f(1, 0, 0)  # 红色箭头
                            glLineWidth(2.0)  # 粗一点

                            dx, dy = ex - sx, ey - sy
                            length = math.hypot(dx, dy)
                            if length > 1e-6:
                                nx, ny = dx / length, dy / length
                                arrow_len = 2.5#max(length * 0.05, 0.1)
                                alpha = math.radians(20)

                                def rotate(vx, vy, ang):
                                    return vx * math.cos(ang) - vy * math.sin(ang), vx * math.sin(ang) + vy * math.cos(ang)

                                r1x, r1y = rotate(nx, ny, alpha)
                                r2x, r2y = rotate(nx, ny, -alpha)

                                # 为了不被深度测试挡住，可以临时关掉
                                glDisable(GL_DEPTH_TEST)

                                glBegin(GL_LINES)
                                # 分支1
                                glVertex3f(ex, ey, ez + 0.001)  # 微微抬高
                                glVertex3f(ex - r1x * arrow_len, ey - r1y * arrow_len, ez + 0.001)
                                # 分支2
                                glVertex3f(ex, ey, ez + 0.001)
                                glVertex3f(ex - r2x * arrow_len, ey - r2y * arrow_len, ez + 0.001)
                                glEnd()

                                # 恢复深度测试
                                glEnable(GL_DEPTH_TEST)

                    # 绘制圆弧实体
                    elif entity.dxftype == 'ARC':
                        center_x = entity.parameters['center_x']
                        center_y = entity.parameters['center_y']
                        radius = entity.parameters['radius']
                        start_angle = math.radians(entity.parameters['start_angle'])
                        end_angle = math.radians(entity.parameters['end_angle'])
                        center_z = 0  # 设置z坐标默认值为0
                        num_segments = 100  # 弧的多边形近似的分段数
                        glBegin(GL_LINES)

                        # 描点连线画圆弧
                        for i in range(1, num_segments + 1):
                            theta = start_angle + (end_angle - start_angle) * (i - 1) / num_segments
                            x_last = center_x + radius * math.cos(theta)
                            y_last = center_y + radius * math.sin(theta)
                            theta = start_angle + (end_angle - start_angle) * i / num_segments
                            x = center_x + radius * math.cos(theta)
                            y = center_y + radius * math.sin(theta)
                            glVertex3f(x, y, 0.0)
                            glVertex3f(x_last, y_last, 0.0)
                        glEnd()

                    # 确保在绘制完后禁用虚线模式
                    if dashed:
                        glDisable(GL_LINE_STIPPLE)

    def toggle_axes(self):
        """切换坐标轴的显示/隐藏状态"""
        self.show_axes = not self.show_axes  # 切换标志
        self.update()  # 重绘视图

    def draw_axes_3d(self):
        """绘制左下角的三维坐标轴"""
        viewport = glGetIntegerv(GL_VIEWPORT)
        w, h = viewport[2], viewport[3]

        glViewport(10, 10, 100, 100)  # 设置视口到左下角的小区域

        glMatrixMode(GL_PROJECTION)
        glPushMatrix()
        glLoadIdentity()
        gluPerspective(45.0, 1.0, 0.1, 10.0)

        glMatrixMode(GL_MODELVIEW)
        glPushMatrix()
        glLoadIdentity()
        glTranslatef(0.0, 0.0, -2.0)  # 放置坐标轴的位置
        glRotatef(self.x_rot, 1.0, 0.0, 0.0)
        glRotatef(self.y_rot, 0.0, 1.0, 0.0)

        # 绘制三轴
        glBegin(GL_LINES)
        # X轴红色
        glColor3f(1.0, 0.0, 0.0)
        glVertex3f(0.0, 0.0, 0.0)
        glVertex3f(1.0, 0.0, 0.0)
        # Y轴绿色
        glColor3f(0.0, 1.0, 0.0)
        glVertex3f(0.0, 0.0, 0.0)
        glVertex3f(0.0, 1.0, 0.0)
        # Z轴蓝色
        glColor3f(0.0, 0.0, 1.0)
        glVertex3f(0.0, 0.0, 0.0)
        glVertex3f(0.0, 0.0, 1.0)
        glEnd()

        glPopMatrix()
        glMatrixMode(GL_PROJECTION)
        glPopMatrix()
        glMatrixMode(GL_MODELVIEW)

        glViewport(0, 0, w, h)  # 还原视口

    def handle_click(self, pos):
        """处理鼠标点击事件以检测对3D空间中实体的交互。"""
        if not self.is_2d_mode:  # 如果不是2D模式，则不处理点击
            return

        # 将屏幕坐标转换为世界坐标
        x, y = pos.x(), self.height() - pos.y()  # 由于OpenGL的坐标原点不同，需要翻转y坐标
        print(x,y)
        points = [(x, y)]#(x,y)鼠标点击坐标，亦是圆心坐标
        max_radius = 10 #最大半径
        num_rings = 5 #同心圆的数量
        base_points = 12 #最小半径圆上的点数
        # 每个同心圆的半径间隔
        radius_step = max_radius / num_rings

        # 遍历每个同心圆
        for i in range(1, num_rings + 1):
            radius = i * radius_step  # 当前圆的半径

            # 随着半径增大，点数量增加
            num_points = base_points + int(radius / max_radius * base_points)

            # 在当前圆上分布点
            for j in range(num_points):
                angle = 2 * math.pi * j / num_points  # 均匀分布角度
                dx = radius * math.cos(angle)
                dy = radius * math.sin(angle)
                points.append((x + dx, y + dy))
        # 定义点击的容差
        tolerance = 2.0  # 设置较高的容差以提高命中检测的准确性
        found_intersection = False  # 标记是否已找到交点
        # 遍历检测到的实体以检查点击是否与任何实体相交
        if hasattr(self, 'ToolPath') and self.ToolPath:
            for x, y in points:  # 遍历所有生成的点
                # 读取鼠标点击位置的深度分量以获取z坐标
                z = glReadPixels(x, y, 1, 1, GL_DEPTH_COMPONENT, GL_FLOAT)[0][0]
                # z = 0.0
                # print(x,y,z)
                # 获取当前的模型视图矩阵和投影矩阵用于坐标转换
                modelview = glGetDoublev(GL_MODELVIEW_MATRIX)
                projection = glGetDoublev(GL_PROJECTION_MATRIX)
                # 获取当前视口配置
                viewport = glGetIntegerv(GL_VIEWPORT)
                # 执行屏幕坐标至世界坐标的反投影
                # 这将2D屏幕位置转换为3D世界位置
                world_coords = gluUnProject(x, y, z, modelview, projection, viewport)
                # print(world_coords)
                for i, sublist in enumerate(self.ToolPath):  # 遍历实体的分组
                    for entity in sublist:  # 遍历每个分组中的单个实体
                        # 检查点击位置是否足够接近以被认为是在实体上
                        if self.is_point_on_entity(world_coords, entity, tolerance):
                            found_intersection = True
                            # 确定实体的类型并打印相关信息
                            if entity.dxftype == 'LINE':
                                print(f"LINE, 起点: {entity.parameters['start_x']},{entity.parameters['start_y']},"
                                      f"终点: {entity.parameters['end_x']},{entity.parameters['end_y']}")
                            elif entity.dxftype == 'ARC':
                                print(f"ARC, 中心: {entity.parameters['center_x']},{entity.parameters['center_y']},"
                                      f"半径: {entity.parameters['radius']},"
                                      f"起始角: {entity.parameters['start_angle']}, 终止角: {entity.parameters['end_angle']}")

                                # 检查并更新高亮显示的实体列表
                            # if entity not in self.highlighted_entities:
                            #    self.highlighted_entities.append(entity)
                            # else:
                            #     self.highlighted_entities.remove(entity)
                            for i, sublist0 in enumerate(self.ToolPath):
                                if entity in sublist0:
                                    if sublist not in self.highlighted_entities:
                                        self.highlighted_entities.append(sublist0)
                                        print(self.ToolPath.index(sublist0))
                                    else:
                                        self.highlighted_entities.remove(sublist0)

                                        # 更新显示
                            self.update()
                            break
                    if found_intersection:
                        break  # 退出当前实体组检测循环
                if found_intersection:
                    break  # 退出所有点的检测循环

    def is_point_on_entity(self, point, entity, tolerance):
        """检查点是否在实体上"""
        if entity.dxftype == 'LINE':
            return self.is_point_on_line(point, entity, tolerance)
        elif entity.dxftype == 'CIRCLE':
            return self.is_point_on_circle(point, entity, tolerance)
        elif entity.dxftype == 'ARC':
            return self.is_point_on_arc(point, entity, tolerance)
        return False

    def is_point_on_line(self, point, entity, tolerance):
        """检查点是否在直线上"""
        x, y, z = point
        x1, y1, z1 = entity.parameters['start_x'], entity.parameters['start_y'], 0
        x2, y2, z2 = entity.parameters['end_x'], entity.parameters['end_y'], 0
        # 计算线段的长度（即：直线的模长）
        line_mag = math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)
        # 如果线段太短（小于容差），返回False，因为这可能是个点而非线段
        if line_mag < tolerance:
            return False

        # 计算点到直线的标量投影因子u（参数化直线方程中的参数）
        u = ((x - x1) * (x2 - x1) + (y - y1) * (y2 - y1)) / (line_mag * line_mag)
        # 如果投影因子u不在0到1之间，点在直线段的延长线或者之外，返回False
        if u < 0.0 or u > 1.0:
            return False

        # 基于投影因子u计算点在直线上的投影坐标（ix, iy）
        ix = x1 + u * (x2 - x1)
        iy = y1 + u * (y2 - y1)

        # 计算原始点到投影点的距离
        distance = math.sqrt((x - ix) ** 2 + (y - iy) ** 2)

        # 如果计算出的距离小于容差，返回True，表示点在直线上
        return distance < tolerance

    def is_point_on_circle(self, point, entity, tolerance):
        """检查点是否在圆上"""
        x, y, z = point
        cx, cy, cz = entity.parameters['center_x'], entity.parameters['center_y'], 0
        radius = entity.parameters['radius']
        distance = math.sqrt((x - cx) ** 2 + (y - cy) ** 2)
        return abs(distance - radius) < tolerance

    def is_point_on_arc(self, point, entity, tolerance):
        """检查点是否在弧上"""
        x, y, z = point
        cx, cy, cz = entity.parameters['center_x'], entity.parameters['center_y'], 0
        radius = entity.parameters['radius']
        start_angle = math.radians(entity.parameters['start_angle'])
        end_angle = math.radians(entity.parameters['end_angle'])
        min_angle = min(start_angle, end_angle)
        max_angle = max(start_angle, end_angle)
        distance = math.sqrt((x - cx) ** 2 + (y - cy) ** 2)
        # print("start", math.degrees(start_angle))
        # print("end", math.degrees(end_angle))
        if abs(distance - radius) < tolerance:
            angle = math.atan2(y - cy, x - cx)
            # print(math.degrees(angle))
            # if angle  < 0:
            #     angle = angle + 2 * math.pi
            # if end_angle  > 2 * math.pi:
            #     if (start_angle <= angle <= 2 * math.pi) or (0 <= angle <= end_angle - 2 * math.pi):
            #         return True

            if min_angle <= angle <= max_angle:
                return True
        return False

class MainWindow(QMainWindow):
    # 定义信号：传递坐标给 OpenGLWidget
    origin_changed = pyqtSignal(float, float)

    def __init__(self):
        super().__init__()
        uic.loadUi('机器人加工.ui', self)
        self.setWindowTitle('机器人型腔铣削加工 ')  # 设置窗口标题
        self.setGeometry(0, 0, 1920, 1080)  # 设置窗口的初始位置和大小

        self.viewer = DXFViewer(self)  # 创建DXFViewer实例
        self.show_Layout.addWidget(self.viewer)  # 将DXFViewer添加到布局中

        self.generate_square.clicked.connect(self.draw_square)  # 连接按钮点击事件到槽函数
        # self.AXES.clicked.connect(self.viewer.toggle_axes)  # 连接按钮点击事件到槽函数
        # self.g41.clicked.connect(self.G41)  # 连接按钮点击事件到槽函数
        # self.g42.clicked.connect(self.G42)  # 连接按钮点击事件到槽函数
        # self.cancel.clicked.connect(self.delete)
        # self.confirm.clicked.connect(self.add)
        self.generate_path.clicked.connect(self.get_path)
        self.generate_gcode.clicked.connect(self.show_gcode)  # 连接按钮点击事件与show_gcode方法
        self.save_gcode.clicked.connect(self.save_gcode_file)  # 连接按钮点击事件与save_gcode_file方法
        self.delete_gcode.clicked.connect(self.Delete_gcode)
        self.delete_path.clicked.connect(self.Delete_path)
        self.tool_magazine.clicked.connect(self.open_table_editor)
        # self.Modle_2d.clicked.connect(self.M2D)
        # self.Modle_2d.setStyleSheet("background-color: rgb(224, 238, 249);")  # 设置初始颜色
        # self.Modle_3d.clicked.connect(self.M3D)
        # self.action_ParameterSettings.triggered.connect(self.open_config_window)
        # self.action_PathOptimize.triggered.connect(self.path_optimize)

        self.length.setValue(300.0)  # 设置 半径补偿 的默认值为0.0
        self.width.setValue(200.0)  # 设置 半径补偿 的默认值为0.0
        # self.tool_offset.setValue(5.0)  # 设置 半径补偿 的默认值为0.0
        self.total_depth.setValue(10.0)  # 设置 快移高度 的默认值为10.0
        # self.depth.setValue(2.0)  # 设置 相邻容差 的默认值为0.1
        # self.out_length.setValue(1.5 * self.tool_offset.value())  # 设置 相邻容差 的默认值为0.1


        # self.tool_zero_x.setValue(0.0)  # 设置 刀具原点 的默认值为0.0
        # self.tool_zero_y.setValue(0.0)  # 设置 刀具原点 的默认值为0.0
        # self.HangHao = "N"

    # def M3D(self):
    #     self.viewer.is_2d_mode = False
    #     self.viewer.set_2d_mode()
    #     self.Modle_3d.setStyleSheet("background-color: rgb(224, 238, 249);")  # 设置初始颜色
    #     self.Modle_2d.setStyleSheet("")  # 设置初始颜色
    #     self.viewer.update()  # 更新视图
    #
    # def M2D(self):
    #     self.viewer.is_2d_mode = True
    #     self.viewer.set_2d_mode()
    #     self.Modle_2d.setStyleSheet("background-color: rgb(224, 238, 249);")  # 设置初始颜色
    #     self.Modle_3d.setStyleSheet("")  # 设置初始颜色
    #     self.viewer.update()  # 更新视图


    # 确认补偿操作
    def open_table_editor(self):
        self.editor = TableEditorWindow()
        # 连接信号，接收确认行数据
        self.editor.rowConfirmed.connect(self.receive_row_data)
        self.editor.show()

    def receive_row_data(self, row_data):
        # 主程序收到数据后的处理
        print(f"收到来自表格编辑器的数据: {row_data}")
        self.tool_offset = 0.5*float(row_data[2])
        # print(self.tool_offset)
        # QMessageBox.information(self, "接收到数据", ", ".join(row_data))

    def add(self):
        copylist = copy.deepcopy(self.viewer.Offsets)
        self.viewer.OffsetsResults.extend(copylist)
        self.viewer.highlighted_entities = []
        self.viewer.Offsets = []
        self.viewer.result = split_entities(
            self.viewer.sort) + self.viewer.Offsets + self.viewer.OffsetsResults  + self.viewer.ToolPath# 更新可操作列表
        self.viewer.update()  # 更新视图

    # 删除线
    def delete(self):
        for sublist in self.viewer.highlighted_entities:
            if sublist in self.viewer.Offsets:
                self.viewer.Offsets.remove(sublist)
                self.viewer.result = split_entities(self.viewer.sort) + self.viewer.Offsets + self.viewer.OffsetsResults + self.viewer.ToolPath
                self.viewer.highlighted_entities.remove(sublist)
            elif sublist in self.viewer.OffsetsResults:
                self.viewer.OffsetsResults.remove(sublist)
                self.viewer.result = split_entities(self.viewer.sort) + self.viewer.Offsets + self.viewer.OffsetsResults + self.viewer.ToolPath
                self.viewer.highlighted_entities.remove(sublist)
        self.viewer.update()  # 更新视图

    # 生成刀具路径
    def get_path(self):
        if hasattr(self, 'tool_offset'):
            self.viewer.ToolPath = []
            self.layer = []
            copylist = copy.deepcopy(self.viewer.OffsetsResults)
            self.viewer.ToolPath.extend(copylist)
            # tool_path(self.viewer.ToolPath, travel_height,self.get_tool_zero())
            self.layer_points = get_point(self.viewer.result, self.tool_offset, self.total_depth.value(),
                               self.total_depth.value(),self.out_length.value())
            self.viewer.ToolPath = create_line_entities(self.layer_points)
            self.viewer.result = split_entities(self.viewer.sort) + self.viewer.Offsets + self.viewer.OffsetsResults + self.viewer.ToolPath  # 更新可操作列表
        else:
            self.update_gcode_text("请先选择刀具")  # 调用更新 G 代码函数显示
        self.viewer.update()  # 更新视图

    # 删除刀具路径
    def Delete_path(self):
        global gcode
        self.viewer.ToolPath = []
        # for i in range(len(self.viewer.OffsetsResults) - 1, -1, -1):
        #     sublist = self.viewer.OffsetsResults[i]
        #     # 检查子列表的第一个 Entity 对象是否存在 'type' 键
        #     if 'type' in sublist[0].parameters:
        #         # 删除符合条件的子列表
        #         del self.viewer.OffsetsResults[i]
        self.viewer.result = split_entities(
            self.viewer.sort) + self.viewer.Offsets + self.viewer.OffsetsResults  + self.viewer.ToolPath# 更新可操作列表
        gcode = ""
        self.update_gcode_text(gcode)  # 调用更新 G 代码函数显示
        self.viewer.update()  # 更新视图

    def get_tool_zero(self):
       return (self.tool_zero_x.value(),self.tool_zero_y.value())

    # 获取 行号格式 的值
    def get_HangHao(self):
        return self.HangHao

    # 显示g代码
    def show_gcode(self):
        global gcode
        if self.viewer.ToolPath:
            gcode = ""
            toolpath = []
            self.update_gcode_text(gcode)  # 调用更新 G 代码函数显示
            # HangHao = self.get_HangHao()
            if not self.viewer.highlighted_entities: #判断高亮列表为空
                gcode = get_gcode(self.layer_points,self.total_depth.value())
            else:
                toolpath = collect_interpolated_toolpaths(self.viewer.highlighted_entities,self.viewer.ToolPath)
                tool_path(toolpath,self.total_depth.value())
                gcode = get_gcode2(toolpath)
            self.update_gcode_text(gcode)  # 调用更新 G 代码函数显示
        else:
            self.update_gcode_text("请先加载 DXF 文件，并进行路径生成")  # 调用更新 G 代码函数显示

    # 删除g代码
    def Delete_gcode(self):
        global gcode
        gcode = ""
        self.update_gcode_text(gcode)  # 调用更新 G 代码函数显示

    # 更新g代码
    def update_gcode_text(self, gcode):  # 更新 G 代码
        self.Gcode.setPlainText(gcode)

    # 保存g代码
    def save_gcode_file(self):  # 保存G代码文件方法
        global gcode
        if self.viewer.ToolPath:
            save_path, _ = QFileDialog.getSaveFileName(self, "保存代码文件", "", "Text Files (*.txt)")  # 获取保存路径和文件名
            if save_path:  # 如果保存路径有效
                with open(save_path, 'w') as file:  # 打开文件以写入
                    file.write(gcode)  # 写入G代码
            else:
                self.update_gcode_text("请先加载 DXF 文件，并进行路径生成")  # 调用更新 G 代码函数显示

    # 打开文件
    def draw_square(self):
        """打开文件对话框，选择DXF文件"""
        global gcode, line_number
        gcode = ""  # 初始化 G 代码字符串
        self.update_gcode_text(gcode)  # 调用更新 G 代码函数显示
        connects1 = []
        connects2 = []
        line_number = 0
        a = self.length.value()
        b = self.width.value()
        self.viewer.load_dxf(a,b)



if __name__ == "__main__":
    app = QApplication(sys.argv)  # 创建应用程序实例
    mainWindow = MainWindow()  # 创建主窗口实例
    mainWindow.show()  # 显示主窗口
    sys.exit(app.exec_())  # 进入应用程序主循环





