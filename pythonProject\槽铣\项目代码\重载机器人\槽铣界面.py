# -*- coding: utf-8 -*-

# Form implementation generated from reading ui file '槽铣界面.ui'
#
# Created by: PyQt5 UI code generator 5.15.10
#
# WARNING: Any manual changes made to this file will be lost when pyuic5 is
# run again.  Do not edit this file unless you know what you are doing.

from PyQt5 import QtCore, QtGui, QtWidgets

class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        MainWindow.setObjectName("MainWindow")
        MainWindow.resize(1920, 1076)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Preferred, QtWidgets.QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(MainWindow.sizePolicy().hasHeightForWidth())
        MainWindow.setSizePolicy(sizePolicy)
        self.centralwidget = QtWidgets.QWidget(MainWindow)
        self.centralwidget.setObjectName("centralwidget")
        self.gridLayout_11 = QtWidgets.QGridLayout(self.centralwidget)
        self.gridLayout_11.setObjectName("gridLayout_11")
        self.label_2 = QtWidgets.QLabel(self.centralwidget)
        font = QtGui.QFont()
        font.setPointSize(25)
        self.label_2.setFont(font)
        self.label_2.setAlignment(QtCore.Qt.AlignCenter)
        self.label_2.setObjectName("label_2")
        self.gridLayout_11.addWidget(self.label_2, 1, 1, 1, 1)
        self.label = QtWidgets.QLabel(self.centralwidget)
        font = QtGui.QFont()
        font.setPointSize(25)
        self.label.setFont(font)
        self.label.setAlignment(QtCore.Qt.AlignCenter)
        self.label.setObjectName("label")
        self.gridLayout_11.addWidget(self.label, 0, 1, 1, 2)
        self.gridLayout = QtWidgets.QGridLayout()
        self.gridLayout.setObjectName("gridLayout")
        self.pushButton_7 = QtWidgets.QPushButton(self.centralwidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.pushButton_7.sizePolicy().hasHeightForWidth())
        self.pushButton_7.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(15)
        self.pushButton_7.setFont(font)
        self.pushButton_7.setObjectName("pushButton_7")
        self.gridLayout.addWidget(self.pushButton_7, 1, 1, 1, 1)
        self.pushButton_5 = QtWidgets.QPushButton(self.centralwidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.pushButton_5.sizePolicy().hasHeightForWidth())
        self.pushButton_5.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(15)
        self.pushButton_5.setFont(font)
        self.pushButton_5.setObjectName("pushButton_5")
        self.gridLayout.addWidget(self.pushButton_5, 1, 2, 1, 1)
        self.pushButton_3 = QtWidgets.QPushButton(self.centralwidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.pushButton_3.sizePolicy().hasHeightForWidth())
        self.pushButton_3.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(15)
        self.pushButton_3.setFont(font)
        self.pushButton_3.setObjectName("pushButton_3")
        self.gridLayout.addWidget(self.pushButton_3, 0, 1, 1, 1)
        self.pushButton_2 = QtWidgets.QPushButton(self.centralwidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.pushButton_2.sizePolicy().hasHeightForWidth())
        self.pushButton_2.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(15)
        self.pushButton_2.setFont(font)
        self.pushButton_2.setObjectName("pushButton_2")
        self.gridLayout.addWidget(self.pushButton_2, 1, 0, 1, 1)
        self.pushButton_6 = QtWidgets.QPushButton(self.centralwidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.pushButton_6.sizePolicy().hasHeightForWidth())
        self.pushButton_6.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(15)
        self.pushButton_6.setFont(font)
        self.pushButton_6.setObjectName("pushButton_6")
        self.gridLayout.addWidget(self.pushButton_6, 0, 2, 1, 1)
        self.pushButton = QtWidgets.QPushButton(self.centralwidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.pushButton.sizePolicy().hasHeightForWidth())
        self.pushButton.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(15)
        self.pushButton.setFont(font)
        self.pushButton.setObjectName("pushButton")
        self.gridLayout.addWidget(self.pushButton, 0, 0, 1, 1)
        self.gridLayout_11.addLayout(self.gridLayout, 0, 0, 2, 1)
        self.textBrowser = QtWidgets.QTextBrowser(self.centralwidget)
        font = QtGui.QFont()
        font.setPointSize(15)
        self.textBrowser.setFont(font)
        self.textBrowser.setObjectName("textBrowser")
        self.gridLayout_11.addWidget(self.textBrowser, 0, 3, 2, 1)
        self.tabWidget = QtWidgets.QTabWidget(self.centralwidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.tabWidget.sizePolicy().hasHeightForWidth())
        self.tabWidget.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.tabWidget.setFont(font)
        self.tabWidget.setCursor(QtGui.QCursor(QtCore.Qt.ArrowCursor))
        self.tabWidget.setFocusPolicy(QtCore.Qt.TabFocus)
        self.tabWidget.setTabPosition(QtWidgets.QTabWidget.North)
        self.tabWidget.setTabShape(QtWidgets.QTabWidget.Rounded)
        self.tabWidget.setIconSize(QtCore.QSize(30, 50))
        self.tabWidget.setObjectName("tabWidget")
        self.tab = QtWidgets.QWidget()
        self.tab.setObjectName("tab")
        self.horizontalLayoutWidget_2 = QtWidgets.QWidget(self.tab)
        self.horizontalLayoutWidget_2.setGeometry(QtCore.QRect(10, 120, 435, 61))
        self.horizontalLayoutWidget_2.setObjectName("horizontalLayoutWidget_2")
        self.horizontalLayout_6 = QtWidgets.QHBoxLayout(self.horizontalLayoutWidget_2)
        self.horizontalLayout_6.setContentsMargins(0, 0, 0, 0)
        self.horizontalLayout_6.setObjectName("horizontalLayout_6")
        self.label_3 = QtWidgets.QLabel(self.horizontalLayoutWidget_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_3.sizePolicy().hasHeightForWidth())
        self.label_3.setSizePolicy(sizePolicy)
        self.label_3.setObjectName("label_3")
        self.horizontalLayout_6.addWidget(self.label_3)
        self.comboBox = QtWidgets.QComboBox(self.horizontalLayoutWidget_2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.comboBox.sizePolicy().hasHeightForWidth())
        self.comboBox.setSizePolicy(sizePolicy)
        self.comboBox.setObjectName("comboBox")
        self.comboBox.addItem("")
        self.comboBox.addItem("")
        self.comboBox.addItem("")
        self.comboBox.addItem("")
        self.comboBox.addItem("")
        self.horizontalLayout_6.addWidget(self.comboBox)
        self.formLayoutWidget = QtWidgets.QWidget(self.tab)
        self.formLayoutWidget.setGeometry(QtCore.QRect(510, 60, 421, 251))
        self.formLayoutWidget.setObjectName("formLayoutWidget")
        self.gridLayout_2 = QtWidgets.QGridLayout(self.formLayoutWidget)
        self.gridLayout_2.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_2.setObjectName("gridLayout_2")
        self.label_24 = QtWidgets.QLabel(self.formLayoutWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_24.sizePolicy().hasHeightForWidth())
        self.label_24.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_24.setFont(font)
        self.label_24.setObjectName("label_24")
        self.gridLayout_2.addWidget(self.label_24, 2, 2, 1, 1)
        self.doubleSpinBox_3 = QtWidgets.QDoubleSpinBox(self.formLayoutWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.doubleSpinBox_3.sizePolicy().hasHeightForWidth())
        self.doubleSpinBox_3.setSizePolicy(sizePolicy)
        self.doubleSpinBox_3.setObjectName("doubleSpinBox_3")
        self.gridLayout_2.addWidget(self.doubleSpinBox_3, 2, 1, 1, 1)
        self.label_5 = QtWidgets.QLabel(self.formLayoutWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_5.sizePolicy().hasHeightForWidth())
        self.label_5.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_5.setFont(font)
        self.label_5.setObjectName("label_5")
        self.gridLayout_2.addWidget(self.label_5, 1, 0, 1, 1)
        self.label_6 = QtWidgets.QLabel(self.formLayoutWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_6.sizePolicy().hasHeightForWidth())
        self.label_6.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_6.setFont(font)
        self.label_6.setObjectName("label_6")
        self.gridLayout_2.addWidget(self.label_6, 2, 0, 1, 1)
        self.doubleSpinBox_2 = QtWidgets.QDoubleSpinBox(self.formLayoutWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.doubleSpinBox_2.sizePolicy().hasHeightForWidth())
        self.doubleSpinBox_2.setSizePolicy(sizePolicy)
        self.doubleSpinBox_2.setObjectName("doubleSpinBox_2")
        self.gridLayout_2.addWidget(self.doubleSpinBox_2, 1, 1, 1, 1)
        self.doubleSpinBox = QtWidgets.QDoubleSpinBox(self.formLayoutWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.doubleSpinBox.sizePolicy().hasHeightForWidth())
        self.doubleSpinBox.setSizePolicy(sizePolicy)
        self.doubleSpinBox.setObjectName("doubleSpinBox")
        self.gridLayout_2.addWidget(self.doubleSpinBox, 0, 1, 1, 1)
        self.label_23 = QtWidgets.QLabel(self.formLayoutWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_23.sizePolicy().hasHeightForWidth())
        self.label_23.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_23.setFont(font)
        self.label_23.setObjectName("label_23")
        self.gridLayout_2.addWidget(self.label_23, 0, 2, 1, 1)
        self.label_25 = QtWidgets.QLabel(self.formLayoutWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_25.sizePolicy().hasHeightForWidth())
        self.label_25.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_25.setFont(font)
        self.label_25.setObjectName("label_25")
        self.gridLayout_2.addWidget(self.label_25, 1, 2, 1, 1)
        self.label_4 = QtWidgets.QLabel(self.formLayoutWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_4.sizePolicy().hasHeightForWidth())
        self.label_4.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_4.setFont(font)
        self.label_4.setObjectName("label_4")
        self.gridLayout_2.addWidget(self.label_4, 0, 0, 1, 1)
        self.btn_back = QtWidgets.QPushButton(self.tab)
        self.btn_back.setGeometry(QtCore.QRect(0, 641, 161, 61))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btn_back.sizePolicy().hasHeightForWidth())
        self.btn_back.setSizePolicy(sizePolicy)
        self.btn_back.setObjectName("btn_back")
        self.pushButton_8 = QtWidgets.QPushButton(self.tab)
        self.pushButton_8.setGeometry(QtCore.QRect(390, 640, 161, 61))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.pushButton_8.sizePolicy().hasHeightForWidth())
        self.pushButton_8.setSizePolicy(sizePolicy)
        self.pushButton_8.setObjectName("pushButton_8")
        self.pushButton_9 = QtWidgets.QPushButton(self.tab)
        self.pushButton_9.setGeometry(QtCore.QRect(590, 640, 161, 61))
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.pushButton_9.sizePolicy().hasHeightForWidth())
        self.pushButton_9.setSizePolicy(sizePolicy)
        self.pushButton_9.setObjectName("pushButton_9")
        self.label_59 = QtWidgets.QLabel(self.tab)
        self.label_59.setGeometry(QtCore.QRect(1310, 620, 261, 51))
        self.label_59.setObjectName("label_59")
        self.label_53 = QtWidgets.QLabel(self.tab)
        self.label_53.setGeometry(QtCore.QRect(1090, 44, 661, 501))
        self.label_53.setText("")
        self.label_53.setPixmap(QtGui.QPixmap(":/try/1.png"))
        self.label_53.setScaledContents(True)
        self.label_53.setObjectName("label_53")
        self.tabWidget.addTab(self.tab, "")
        self.tab_2 = QtWidgets.QWidget()
        self.tab_2.setObjectName("tab_2")
        self.pushButton_10 = QtWidgets.QPushButton(self.tab_2)
        self.pushButton_10.setGeometry(QtCore.QRect(590, 640, 161, 61))
        self.pushButton_10.setObjectName("pushButton_10")
        self.btn_back111 = QtWidgets.QPushButton(self.tab_2)
        self.btn_back111.setGeometry(QtCore.QRect(0, 641, 161, 61))
        self.btn_back111.setObjectName("btn_back111")
        self.pushButton_12 = QtWidgets.QPushButton(self.tab_2)
        self.pushButton_12.setGeometry(QtCore.QRect(390, 640, 161, 61))
        self.pushButton_12.setObjectName("pushButton_12")
        self.pushButton_13 = QtWidgets.QPushButton(self.tab_2)
        self.pushButton_13.setGeometry(QtCore.QRect(190, 640, 161, 61))
        self.pushButton_13.setObjectName("pushButton_13")
        self.layoutWidget = QtWidgets.QWidget(self.tab_2)
        self.layoutWidget.setGeometry(QtCore.QRect(920, 120, 111, 278))
        self.layoutWidget.setObjectName("layoutWidget")
        self.verticalLayout = QtWidgets.QVBoxLayout(self.layoutWidget)
        self.verticalLayout.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout.setSpacing(22)
        self.verticalLayout.setObjectName("verticalLayout")
        self.label_13 = QtWidgets.QLabel(self.layoutWidget)
        font = QtGui.QFont()
        font.setPointSize(10)
        font.setBold(False)
        font.setWeight(50)
        self.label_13.setFont(font)
        self.label_13.setAlignment(QtCore.Qt.AlignCenter)
        self.label_13.setObjectName("label_13")
        self.verticalLayout.addWidget(self.label_13)
        self.textBrowser_2 = QtWidgets.QTextBrowser(self.layoutWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.textBrowser_2.sizePolicy().hasHeightForWidth())
        self.textBrowser_2.setSizePolicy(sizePolicy)
        self.textBrowser_2.setObjectName("textBrowser_2")
        self.verticalLayout.addWidget(self.textBrowser_2)
        self.pushButton_14 = QtWidgets.QPushButton(self.layoutWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.pushButton_14.sizePolicy().hasHeightForWidth())
        self.pushButton_14.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(10)
        font.setBold(False)
        font.setWeight(50)
        self.pushButton_14.setFont(font)
        self.pushButton_14.setObjectName("pushButton_14")
        self.verticalLayout.addWidget(self.pushButton_14)
        self.pushButton_15 = QtWidgets.QPushButton(self.layoutWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.pushButton_15.sizePolicy().hasHeightForWidth())
        self.pushButton_15.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(10)
        font.setBold(False)
        font.setWeight(50)
        self.pushButton_15.setFont(font)
        self.pushButton_15.setObjectName("pushButton_15")
        self.verticalLayout.addWidget(self.pushButton_15)
        self.pushButton_16 = QtWidgets.QPushButton(self.layoutWidget)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.pushButton_16.sizePolicy().hasHeightForWidth())
        self.pushButton_16.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(10)
        font.setBold(False)
        font.setWeight(50)
        self.pushButton_16.setFont(font)
        self.pushButton_16.setObjectName("pushButton_16")
        self.verticalLayout.addWidget(self.pushButton_16)
        self.verticalLayoutWidget_3 = QtWidgets.QWidget(self.tab_2)
        self.verticalLayoutWidget_3.setGeometry(QtCore.QRect(1150, 0, 721, 661))
        self.verticalLayoutWidget_3.setObjectName("verticalLayoutWidget_3")
        self.verticalLayout_5 = QtWidgets.QVBoxLayout(self.verticalLayoutWidget_3)
        self.verticalLayout_5.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_5.setObjectName("verticalLayout_5")
        self.graphicsView_4 = QtWidgets.QGraphicsView(self.verticalLayoutWidget_3)
        self.graphicsView_4.setObjectName("graphicsView_4")
        self.verticalLayout_5.addWidget(self.graphicsView_4)
        self.label_55 = QtWidgets.QLabel(self.verticalLayoutWidget_3)
        self.label_55.setObjectName("label_55")
        self.verticalLayout_5.addWidget(self.label_55, 0, QtCore.Qt.AlignHCenter)
        self.formLayoutWidget_9 = QtWidgets.QWidget(self.tab_2)
        self.formLayoutWidget_9.setGeometry(QtCore.QRect(110, 110, 641, 241))
        self.formLayoutWidget_9.setObjectName("formLayoutWidget_9")
        self.formLayout = QtWidgets.QFormLayout(self.formLayoutWidget_9)
        self.formLayout.setContentsMargins(0, 0, 0, 0)
        self.formLayout.setObjectName("formLayout")
        self.gridLayout_4 = QtWidgets.QGridLayout()
        self.gridLayout_4.setObjectName("gridLayout_4")
        self.label_10 = QtWidgets.QLabel(self.formLayoutWidget_9)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_10.sizePolicy().hasHeightForWidth())
        self.label_10.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_10.setFont(font)
        self.label_10.setObjectName("label_10")
        self.gridLayout_4.addWidget(self.label_10, 0, 0, 1, 1)
        self.label_31 = QtWidgets.QLabel(self.formLayoutWidget_9)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_31.sizePolicy().hasHeightForWidth())
        self.label_31.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_31.setFont(font)
        self.label_31.setObjectName("label_31")
        self.gridLayout_4.addWidget(self.label_31, 2, 2, 1, 1)
        self.horizontalLayout = QtWidgets.QHBoxLayout()
        self.horizontalLayout.setObjectName("horizontalLayout")
        self.radioButton_2 = QtWidgets.QRadioButton(self.formLayoutWidget_9)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.radioButton_2.sizePolicy().hasHeightForWidth())
        self.radioButton_2.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(10)
        font.setBold(False)
        font.setWeight(50)
        self.radioButton_2.setFont(font)
        self.radioButton_2.setObjectName("radioButton_2")
        self.horizontalLayout.addWidget(self.radioButton_2)
        self.radioButton = QtWidgets.QRadioButton(self.formLayoutWidget_9)
        font = QtGui.QFont()
        font.setPointSize(10)
        font.setBold(False)
        font.setWeight(50)
        self.radioButton.setFont(font)
        self.radioButton.setObjectName("radioButton")
        self.horizontalLayout.addWidget(self.radioButton)
        self.gridLayout_4.addLayout(self.horizontalLayout, 1, 1, 1, 1)
        self.label_11 = QtWidgets.QLabel(self.formLayoutWidget_9)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_11.sizePolicy().hasHeightForWidth())
        self.label_11.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_11.setFont(font)
        self.label_11.setObjectName("label_11")
        self.gridLayout_4.addWidget(self.label_11, 1, 0, 1, 1)
        self.label_12 = QtWidgets.QLabel(self.formLayoutWidget_9)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_12.sizePolicy().hasHeightForWidth())
        self.label_12.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_12.setFont(font)
        self.label_12.setObjectName("label_12")
        self.gridLayout_4.addWidget(self.label_12, 2, 0, 1, 1)
        self.spinBox = QtWidgets.QSpinBox(self.formLayoutWidget_9)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.spinBox.sizePolicy().hasHeightForWidth())
        self.spinBox.setSizePolicy(sizePolicy)
        self.spinBox.setObjectName("spinBox")
        self.gridLayout_4.addWidget(self.spinBox, 0, 1, 1, 1)
        self.doubleSpinBox_9 = QtWidgets.QDoubleSpinBox(self.formLayoutWidget_9)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.doubleSpinBox_9.sizePolicy().hasHeightForWidth())
        self.doubleSpinBox_9.setSizePolicy(sizePolicy)
        self.doubleSpinBox_9.setObjectName("doubleSpinBox_9")
        self.gridLayout_4.addWidget(self.doubleSpinBox_9, 2, 1, 1, 1)
        self.formLayout.setLayout(0, QtWidgets.QFormLayout.FieldRole, self.gridLayout_4)
        self.gridLayout_3 = QtWidgets.QGridLayout()
        self.gridLayout_3.setObjectName("gridLayout_3")
        self.label_30 = QtWidgets.QLabel(self.formLayoutWidget_9)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_30.sizePolicy().hasHeightForWidth())
        self.label_30.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_30.setFont(font)
        self.label_30.setObjectName("label_30")
        self.gridLayout_3.addWidget(self.label_30, 1, 2, 1, 1)
        self.doubleSpinBox_14 = QtWidgets.QDoubleSpinBox(self.formLayoutWidget_9)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.doubleSpinBox_14.sizePolicy().hasHeightForWidth())
        self.doubleSpinBox_14.setSizePolicy(sizePolicy)
        self.doubleSpinBox_14.setObjectName("doubleSpinBox_14")
        self.gridLayout_3.addWidget(self.doubleSpinBox_14, 1, 1, 1, 1)
        self.label_28 = QtWidgets.QLabel(self.formLayoutWidget_9)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_28.sizePolicy().hasHeightForWidth())
        self.label_28.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_28.setFont(font)
        self.label_28.setObjectName("label_28")
        self.gridLayout_3.addWidget(self.label_28, 0, 0, 1, 1)
        self.doubleSpinBox_16 = QtWidgets.QDoubleSpinBox(self.formLayoutWidget_9)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.doubleSpinBox_16.sizePolicy().hasHeightForWidth())
        self.doubleSpinBox_16.setSizePolicy(sizePolicy)
        self.doubleSpinBox_16.setObjectName("doubleSpinBox_16")
        self.gridLayout_3.addWidget(self.doubleSpinBox_16, 2, 1, 1, 1)
        self.label_29 = QtWidgets.QLabel(self.formLayoutWidget_9)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_29.sizePolicy().hasHeightForWidth())
        self.label_29.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_29.setFont(font)
        self.label_29.setObjectName("label_29")
        self.gridLayout_3.addWidget(self.label_29, 0, 2, 1, 1)
        self.label_32 = QtWidgets.QLabel(self.formLayoutWidget_9)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_32.sizePolicy().hasHeightForWidth())
        self.label_32.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_32.setFont(font)
        self.label_32.setObjectName("label_32")
        self.gridLayout_3.addWidget(self.label_32, 2, 2, 1, 1)
        self.label_27 = QtWidgets.QLabel(self.formLayoutWidget_9)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_27.sizePolicy().hasHeightForWidth())
        self.label_27.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_27.setFont(font)
        self.label_27.setObjectName("label_27")
        self.gridLayout_3.addWidget(self.label_27, 2, 0, 1, 1)
        self.label_26 = QtWidgets.QLabel(self.formLayoutWidget_9)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_26.sizePolicy().hasHeightForWidth())
        self.label_26.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_26.setFont(font)
        self.label_26.setObjectName("label_26")
        self.gridLayout_3.addWidget(self.label_26, 1, 0, 1, 1)
        self.doubleSpinBox_15 = QtWidgets.QDoubleSpinBox(self.formLayoutWidget_9)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.doubleSpinBox_15.sizePolicy().hasHeightForWidth())
        self.doubleSpinBox_15.setSizePolicy(sizePolicy)
        self.doubleSpinBox_15.setObjectName("doubleSpinBox_15")
        self.gridLayout_3.addWidget(self.doubleSpinBox_15, 0, 1, 1, 1)
        self.formLayout.setLayout(0, QtWidgets.QFormLayout.LabelRole, self.gridLayout_3)
        self.tabWidget.addTab(self.tab_2, "")
        self.tab_3 = QtWidgets.QWidget()
        self.tab_3.setObjectName("tab_3")
        self.pushButton_17 = QtWidgets.QPushButton(self.tab_3)
        self.pushButton_17.setGeometry(QtCore.QRect(190, 640, 161, 61))
        self.pushButton_17.setObjectName("pushButton_17")
        self.btn_back111_2 = QtWidgets.QPushButton(self.tab_3)
        self.btn_back111_2.setGeometry(QtCore.QRect(0, 641, 161, 61))
        self.btn_back111_2.setObjectName("btn_back111_2")
        self.pushButton_18 = QtWidgets.QPushButton(self.tab_3)
        self.pushButton_18.setGeometry(QtCore.QRect(390, 640, 161, 61))
        self.pushButton_18.setObjectName("pushButton_18")
        self.pushButton_11 = QtWidgets.QPushButton(self.tab_3)
        self.pushButton_11.setGeometry(QtCore.QRect(590, 640, 161, 61))
        self.pushButton_11.setObjectName("pushButton_11")
        self.formLayoutWidget_4 = QtWidgets.QWidget(self.tab_3)
        self.formLayoutWidget_4.setGeometry(QtCore.QRect(20, 130, 361, 161))
        self.formLayoutWidget_4.setObjectName("formLayoutWidget_4")
        self.gridLayout_5 = QtWidgets.QGridLayout(self.formLayoutWidget_4)
        self.gridLayout_5.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_5.setObjectName("gridLayout_5")
        self.label_16 = QtWidgets.QLabel(self.formLayoutWidget_4)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_16.setFont(font)
        self.label_16.setObjectName("label_16")
        self.gridLayout_5.addWidget(self.label_16, 1, 0, 1, 1)
        self.horizontalLayout_3 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_3.setObjectName("horizontalLayout_3")
        self.radioButton_5 = QtWidgets.QRadioButton(self.formLayoutWidget_4)
        font = QtGui.QFont()
        font.setPointSize(10)
        font.setBold(False)
        font.setWeight(50)
        self.radioButton_5.setFont(font)
        self.radioButton_5.setObjectName("radioButton_5")
        self.horizontalLayout_3.addWidget(self.radioButton_5)
        self.radioButton_6 = QtWidgets.QRadioButton(self.formLayoutWidget_4)
        font = QtGui.QFont()
        font.setPointSize(10)
        font.setBold(False)
        font.setWeight(50)
        self.radioButton_6.setFont(font)
        self.radioButton_6.setObjectName("radioButton_6")
        self.horizontalLayout_3.addWidget(self.radioButton_6)
        self.gridLayout_5.addLayout(self.horizontalLayout_3, 0, 1, 1, 1)
        self.doubleSpinBox_10 = QtWidgets.QDoubleSpinBox(self.formLayoutWidget_4)
        self.doubleSpinBox_10.setObjectName("doubleSpinBox_10")
        self.gridLayout_5.addWidget(self.doubleSpinBox_10, 2, 1, 1, 1)
        self.label_17 = QtWidgets.QLabel(self.formLayoutWidget_4)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_17.setFont(font)
        self.label_17.setObjectName("label_17")
        self.gridLayout_5.addWidget(self.label_17, 2, 0, 1, 1)
        self.label_15 = QtWidgets.QLabel(self.formLayoutWidget_4)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_15.setFont(font)
        self.label_15.setObjectName("label_15")
        self.gridLayout_5.addWidget(self.label_15, 0, 0, 1, 1)
        self.horizontalLayout_4 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_4.setObjectName("horizontalLayout_4")
        self.radioButton_7 = QtWidgets.QRadioButton(self.formLayoutWidget_4)
        font = QtGui.QFont()
        font.setPointSize(10)
        font.setBold(False)
        font.setWeight(50)
        self.radioButton_7.setFont(font)
        self.radioButton_7.setObjectName("radioButton_7")
        self.horizontalLayout_4.addWidget(self.radioButton_7)
        self.radioButton_8 = QtWidgets.QRadioButton(self.formLayoutWidget_4)
        font = QtGui.QFont()
        font.setPointSize(10)
        font.setBold(False)
        font.setWeight(50)
        self.radioButton_8.setFont(font)
        self.radioButton_8.setObjectName("radioButton_8")
        self.horizontalLayout_4.addWidget(self.radioButton_8)
        self.gridLayout_5.addLayout(self.horizontalLayout_4, 1, 1, 1, 1)
        self.label_33 = QtWidgets.QLabel(self.formLayoutWidget_4)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_33.setFont(font)
        self.label_33.setObjectName("label_33")
        self.gridLayout_5.addWidget(self.label_33, 2, 2, 1, 1)
        self.formLayoutWidget_5 = QtWidgets.QWidget(self.tab_3)
        self.formLayoutWidget_5.setGeometry(QtCore.QRect(400, 130, 441, 161))
        self.formLayoutWidget_5.setObjectName("formLayoutWidget_5")
        self.gridLayout_6 = QtWidgets.QGridLayout(self.formLayoutWidget_5)
        self.gridLayout_6.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_6.setObjectName("gridLayout_6")
        self.label_20 = QtWidgets.QLabel(self.formLayoutWidget_5)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_20.setFont(font)
        self.label_20.setObjectName("label_20")
        self.gridLayout_6.addWidget(self.label_20, 2, 0, 1, 1)
        self.doubleSpinBox_12 = QtWidgets.QDoubleSpinBox(self.formLayoutWidget_5)
        self.doubleSpinBox_12.setObjectName("doubleSpinBox_12")
        self.gridLayout_6.addWidget(self.doubleSpinBox_12, 1, 1, 1, 1)
        self.label_18 = QtWidgets.QLabel(self.formLayoutWidget_5)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_18.setFont(font)
        self.label_18.setObjectName("label_18")
        self.gridLayout_6.addWidget(self.label_18, 0, 0, 1, 1)
        self.label_19 = QtWidgets.QLabel(self.formLayoutWidget_5)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_19.setFont(font)
        self.label_19.setObjectName("label_19")
        self.gridLayout_6.addWidget(self.label_19, 1, 0, 1, 1)
        self.horizontalLayout_5 = QtWidgets.QHBoxLayout()
        self.horizontalLayout_5.setObjectName("horizontalLayout_5")
        self.radioButton_9 = QtWidgets.QRadioButton(self.formLayoutWidget_5)
        font = QtGui.QFont()
        font.setPointSize(10)
        font.setBold(False)
        font.setWeight(50)
        self.radioButton_9.setFont(font)
        self.radioButton_9.setObjectName("radioButton_9")
        self.horizontalLayout_5.addWidget(self.radioButton_9)
        self.radioButton_11 = QtWidgets.QRadioButton(self.formLayoutWidget_5)
        font = QtGui.QFont()
        font.setPointSize(10)
        font.setBold(False)
        font.setWeight(50)
        self.radioButton_11.setFont(font)
        self.radioButton_11.setObjectName("radioButton_11")
        self.horizontalLayout_5.addWidget(self.radioButton_11)
        self.radioButton_10 = QtWidgets.QRadioButton(self.formLayoutWidget_5)
        font = QtGui.QFont()
        font.setPointSize(10)
        font.setBold(False)
        font.setWeight(50)
        self.radioButton_10.setFont(font)
        self.radioButton_10.setObjectName("radioButton_10")
        self.horizontalLayout_5.addWidget(self.radioButton_10)
        self.gridLayout_6.addLayout(self.horizontalLayout_5, 0, 1, 1, 1)
        self.doubleSpinBox_11 = QtWidgets.QDoubleSpinBox(self.formLayoutWidget_5)
        self.doubleSpinBox_11.setObjectName("doubleSpinBox_11")
        self.gridLayout_6.addWidget(self.doubleSpinBox_11, 2, 1, 1, 1)
        self.label_34 = QtWidgets.QLabel(self.formLayoutWidget_5)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_34.setFont(font)
        self.label_34.setObjectName("label_34")
        self.gridLayout_6.addWidget(self.label_34, 1, 2, 1, 1)
        self.label_35 = QtWidgets.QLabel(self.formLayoutWidget_5)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_35.setFont(font)
        self.label_35.setObjectName("label_35")
        self.gridLayout_6.addWidget(self.label_35, 2, 2, 1, 1)
        self.verticalLayoutWidget_2 = QtWidgets.QWidget(self.tab_3)
        self.verticalLayoutWidget_2.setGeometry(QtCore.QRect(890, 20, 801, 671))
        self.verticalLayoutWidget_2.setObjectName("verticalLayoutWidget_2")
        self.verticalLayout_4 = QtWidgets.QVBoxLayout(self.verticalLayoutWidget_2)
        self.verticalLayout_4.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_4.setObjectName("verticalLayout_4")
        self.graphicsView_3 = QtWidgets.QGraphicsView(self.verticalLayoutWidget_2)
        self.graphicsView_3.setObjectName("graphicsView_3")
        self.verticalLayout_4.addWidget(self.graphicsView_3)
        self.graphicsView_2 = QtWidgets.QGraphicsView(self.verticalLayoutWidget_2)
        self.graphicsView_2.setObjectName("graphicsView_2")
        self.verticalLayout_4.addWidget(self.graphicsView_2)
        self.label_54 = QtWidgets.QLabel(self.verticalLayoutWidget_2)
        self.label_54.setObjectName("label_54")
        self.verticalLayout_4.addWidget(self.label_54, 0, QtCore.Qt.AlignHCenter)
        self.tabWidget.addTab(self.tab_3, "")
        self.tab_4 = QtWidgets.QWidget()
        self.tab_4.setObjectName("tab_4")
        self.pushButton_19 = QtWidgets.QPushButton(self.tab_4)
        self.pushButton_19.setGeometry(QtCore.QRect(190, 640, 161, 61))
        self.pushButton_19.setObjectName("pushButton_19")
        self.btn_back111_3 = QtWidgets.QPushButton(self.tab_4)
        self.btn_back111_3.setGeometry(QtCore.QRect(0, 641, 161, 61))
        self.btn_back111_3.setObjectName("btn_back111_3")
        self.pushButton_20 = QtWidgets.QPushButton(self.tab_4)
        self.pushButton_20.setGeometry(QtCore.QRect(390, 640, 161, 61))
        self.pushButton_20.setObjectName("pushButton_20")
        self.pushButton_21 = QtWidgets.QPushButton(self.tab_4)
        self.pushButton_21.setGeometry(QtCore.QRect(590, 640, 161, 61))
        self.pushButton_21.setObjectName("pushButton_21")
        self.formLayoutWidget_2 = QtWidgets.QWidget(self.tab_4)
        self.formLayoutWidget_2.setGeometry(QtCore.QRect(30, 120, 435, 182))
        self.formLayoutWidget_2.setObjectName("formLayoutWidget_2")
        self.gridLayout_7 = QtWidgets.QGridLayout(self.formLayoutWidget_2)
        self.gridLayout_7.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_7.setObjectName("gridLayout_7")
        self.label_36 = QtWidgets.QLabel(self.formLayoutWidget_2)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_36.setFont(font)
        self.label_36.setObjectName("label_36")
        self.gridLayout_7.addWidget(self.label_36, 0, 2, 1, 1)
        self.doubleSpinBox_5 = QtWidgets.QDoubleSpinBox(self.formLayoutWidget_2)
        self.doubleSpinBox_5.setObjectName("doubleSpinBox_5")
        self.gridLayout_7.addWidget(self.doubleSpinBox_5, 0, 1, 1, 1)
        self.label_8 = QtWidgets.QLabel(self.formLayoutWidget_2)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_8.setFont(font)
        self.label_8.setObjectName("label_8")
        self.gridLayout_7.addWidget(self.label_8, 3, 0, 1, 1)
        self.doubleSpinBox_4 = QtWidgets.QDoubleSpinBox(self.formLayoutWidget_2)
        self.doubleSpinBox_4.setObjectName("doubleSpinBox_4")
        self.gridLayout_7.addWidget(self.doubleSpinBox_4, 2, 1, 1, 1)
        self.label_37 = QtWidgets.QLabel(self.formLayoutWidget_2)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_37.setFont(font)
        self.label_37.setObjectName("label_37")
        self.gridLayout_7.addWidget(self.label_37, 2, 2, 1, 1)
        self.spinBox_2 = QtWidgets.QSpinBox(self.formLayoutWidget_2)
        self.spinBox_2.setObjectName("spinBox_2")
        self.gridLayout_7.addWidget(self.spinBox_2, 3, 1, 1, 1)
        self.label_7 = QtWidgets.QLabel(self.formLayoutWidget_2)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_7.setFont(font)
        self.label_7.setObjectName("label_7")
        self.gridLayout_7.addWidget(self.label_7, 2, 0, 1, 1)
        self.label_9 = QtWidgets.QLabel(self.formLayoutWidget_2)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_9.setFont(font)
        self.label_9.setObjectName("label_9")
        self.gridLayout_7.addWidget(self.label_9, 0, 0, 1, 1)
        self.label_58 = QtWidgets.QLabel(self.formLayoutWidget_2)
        self.label_58.setObjectName("label_58")
        self.gridLayout_7.addWidget(self.label_58, 3, 2, 1, 1)
        self.formLayoutWidget_6 = QtWidgets.QWidget(self.tab_4)
        self.formLayoutWidget_6.setGeometry(QtCore.QRect(510, 110, 385, 191))
        self.formLayoutWidget_6.setObjectName("formLayoutWidget_6")
        self.gridLayout_8 = QtWidgets.QGridLayout(self.formLayoutWidget_6)
        self.gridLayout_8.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_8.setObjectName("gridLayout_8")
        self.doubleSpinBox_6 = QtWidgets.QDoubleSpinBox(self.formLayoutWidget_6)
        self.doubleSpinBox_6.setObjectName("doubleSpinBox_6")
        self.gridLayout_8.addWidget(self.doubleSpinBox_6, 1, 1, 1, 1)
        self.label_14 = QtWidgets.QLabel(self.formLayoutWidget_6)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_14.setFont(font)
        self.label_14.setObjectName("label_14")
        self.gridLayout_8.addWidget(self.label_14, 1, 0, 1, 1)
        self.label_40 = QtWidgets.QLabel(self.formLayoutWidget_6)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_40.setFont(font)
        self.label_40.setObjectName("label_40")
        self.gridLayout_8.addWidget(self.label_40, 2, 2, 1, 1)
        self.label_22 = QtWidgets.QLabel(self.formLayoutWidget_6)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_22.setFont(font)
        self.label_22.setObjectName("label_22")
        self.gridLayout_8.addWidget(self.label_22, 0, 0, 1, 1)
        self.doubleSpinBox_7 = QtWidgets.QDoubleSpinBox(self.formLayoutWidget_6)
        self.doubleSpinBox_7.setObjectName("doubleSpinBox_7")
        self.gridLayout_8.addWidget(self.doubleSpinBox_7, 0, 1, 1, 1)
        self.label_21 = QtWidgets.QLabel(self.formLayoutWidget_6)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_21.setFont(font)
        self.label_21.setObjectName("label_21")
        self.gridLayout_8.addWidget(self.label_21, 2, 0, 1, 1)
        self.label_41 = QtWidgets.QLabel(self.formLayoutWidget_6)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_41.setFont(font)
        self.label_41.setObjectName("label_41")
        self.gridLayout_8.addWidget(self.label_41, 4, 0, 1, 1)
        self.label_38 = QtWidgets.QLabel(self.formLayoutWidget_6)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_38.setFont(font)
        self.label_38.setObjectName("label_38")
        self.gridLayout_8.addWidget(self.label_38, 0, 2, 1, 1)
        self.label_39 = QtWidgets.QLabel(self.formLayoutWidget_6)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_39.setFont(font)
        self.label_39.setObjectName("label_39")
        self.gridLayout_8.addWidget(self.label_39, 1, 2, 1, 1)
        self.doubleSpinBox_8 = QtWidgets.QDoubleSpinBox(self.formLayoutWidget_6)
        self.doubleSpinBox_8.setObjectName("doubleSpinBox_8")
        self.gridLayout_8.addWidget(self.doubleSpinBox_8, 2, 1, 1, 1)
        self.doubleSpinBox_13 = QtWidgets.QDoubleSpinBox(self.formLayoutWidget_6)
        self.doubleSpinBox_13.setObjectName("doubleSpinBox_13")
        self.gridLayout_8.addWidget(self.doubleSpinBox_13, 4, 1, 1, 1)
        self.label_42 = QtWidgets.QLabel(self.formLayoutWidget_6)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_42.setFont(font)
        self.label_42.setObjectName("label_42")
        self.gridLayout_8.addWidget(self.label_42, 4, 2, 1, 1)
        self.verticalLayoutWidget_4 = QtWidgets.QWidget(self.tab_4)
        self.verticalLayoutWidget_4.setGeometry(QtCore.QRect(950, 20, 811, 651))
        self.verticalLayoutWidget_4.setObjectName("verticalLayoutWidget_4")
        self.verticalLayout_6 = QtWidgets.QVBoxLayout(self.verticalLayoutWidget_4)
        self.verticalLayout_6.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_6.setObjectName("verticalLayout_6")
        self.graphicsView_5 = QtWidgets.QGraphicsView(self.verticalLayoutWidget_4)
        self.graphicsView_5.setObjectName("graphicsView_5")
        self.verticalLayout_6.addWidget(self.graphicsView_5)
        self.label_56 = QtWidgets.QLabel(self.verticalLayoutWidget_4)
        self.label_56.setObjectName("label_56")
        self.verticalLayout_6.addWidget(self.label_56, 0, QtCore.Qt.AlignHCenter)
        self.tabWidget.addTab(self.tab_4, "")
        self.tab_5 = QtWidgets.QWidget()
        self.tab_5.setObjectName("tab_5")
        self.pushButton_22 = QtWidgets.QPushButton(self.tab_5)
        self.pushButton_22.setGeometry(QtCore.QRect(590, 640, 161, 61))
        self.pushButton_22.setObjectName("pushButton_22")
        self.pushButton_23 = QtWidgets.QPushButton(self.tab_5)
        self.pushButton_23.setGeometry(QtCore.QRect(390, 640, 161, 61))
        self.pushButton_23.setObjectName("pushButton_23")
        self.formLayoutWidget_8 = QtWidgets.QWidget(self.tab_5)
        self.formLayoutWidget_8.setGeometry(QtCore.QRect(280, 60, 318, 221))
        self.formLayoutWidget_8.setObjectName("formLayoutWidget_8")
        self.gridLayout_9 = QtWidgets.QGridLayout(self.formLayoutWidget_8)
        self.gridLayout_9.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_9.setObjectName("gridLayout_9")
        self.label_43 = QtWidgets.QLabel(self.formLayoutWidget_8)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_43.setFont(font)
        self.label_43.setObjectName("label_43")
        self.gridLayout_9.addWidget(self.label_43, 3, 0, 1, 1)
        self.textBrowser_3 = QtWidgets.QTextBrowser(self.formLayoutWidget_8)
        self.textBrowser_3.setObjectName("textBrowser_3")
        self.gridLayout_9.addWidget(self.textBrowser_3, 0, 2, 1, 1)
        self.label_45 = QtWidgets.QLabel(self.formLayoutWidget_8)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_45.setFont(font)
        self.label_45.setObjectName("label_45")
        self.gridLayout_9.addWidget(self.label_45, 0, 0, 1, 1)
        spacerItem = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.gridLayout_9.addItem(spacerItem, 2, 0, 1, 1)
        self.label_49 = QtWidgets.QLabel(self.formLayoutWidget_8)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_49.setFont(font)
        self.label_49.setObjectName("label_49")
        self.gridLayout_9.addWidget(self.label_49, 3, 3, 1, 1)
        self.textBrowser_5 = QtWidgets.QTextBrowser(self.formLayoutWidget_8)
        self.textBrowser_5.setObjectName("textBrowser_5")
        self.gridLayout_9.addWidget(self.textBrowser_5, 3, 2, 1, 1)
        self.label_48 = QtWidgets.QLabel(self.formLayoutWidget_8)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_48.setFont(font)
        self.label_48.setObjectName("label_48")
        self.gridLayout_9.addWidget(self.label_48, 0, 3, 1, 1)
        spacerItem1 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.gridLayout_9.addItem(spacerItem1, 1, 0, 1, 1)
        self.btn_back111_4 = QtWidgets.QPushButton(self.tab_5)
        self.btn_back111_4.setGeometry(QtCore.QRect(0, 640, 161, 61))
        self.btn_back111_4.setObjectName("btn_back111_4")
        self.pushButton_24 = QtWidgets.QPushButton(self.tab_5)
        self.pushButton_24.setGeometry(QtCore.QRect(190, 640, 161, 61))
        self.pushButton_24.setObjectName("pushButton_24")
        self.layoutWidget1 = QtWidgets.QWidget(self.tab_5)
        self.layoutWidget1.setGeometry(QtCore.QRect(610, 60, 302, 221))
        self.layoutWidget1.setObjectName("layoutWidget1")
        self.gridLayout_10 = QtWidgets.QGridLayout(self.layoutWidget1)
        self.gridLayout_10.setContentsMargins(0, 0, 0, 0)
        self.gridLayout_10.setObjectName("gridLayout_10")
        spacerItem2 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.gridLayout_10.addItem(spacerItem2, 1, 0, 1, 1)
        self.label_50 = QtWidgets.QLabel(self.layoutWidget1)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_50.setFont(font)
        self.label_50.setObjectName("label_50")
        self.gridLayout_10.addWidget(self.label_50, 3, 3, 1, 1)
        self.label_44 = QtWidgets.QLabel(self.layoutWidget1)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_44.setFont(font)
        self.label_44.setObjectName("label_44")
        self.gridLayout_10.addWidget(self.label_44, 0, 3, 1, 1)
        self.textBrowser_6 = QtWidgets.QTextBrowser(self.layoutWidget1)
        self.textBrowser_6.setObjectName("textBrowser_6")
        self.gridLayout_10.addWidget(self.textBrowser_6, 0, 2, 1, 1)
        self.textBrowser_4 = QtWidgets.QTextBrowser(self.layoutWidget1)
        self.textBrowser_4.setObjectName("textBrowser_4")
        self.gridLayout_10.addWidget(self.textBrowser_4, 3, 2, 1, 1)
        self.label_47 = QtWidgets.QLabel(self.layoutWidget1)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_47.setFont(font)
        self.label_47.setObjectName("label_47")
        self.gridLayout_10.addWidget(self.label_47, 3, 0, 1, 1)
        self.label_46 = QtWidgets.QLabel(self.layoutWidget1)
        font = QtGui.QFont()
        font.setPointSize(20)
        font.setBold(False)
        font.setWeight(50)
        self.label_46.setFont(font)
        self.label_46.setObjectName("label_46")
        self.gridLayout_10.addWidget(self.label_46, 0, 0, 1, 1)
        spacerItem3 = QtWidgets.QSpacerItem(20, 40, QtWidgets.QSizePolicy.Minimum, QtWidgets.QSizePolicy.Expanding)
        self.gridLayout_10.addItem(spacerItem3, 2, 0, 1, 1)
        self.label_51 = QtWidgets.QLabel(self.tab_5)
        self.label_51.setGeometry(QtCore.QRect(540, 10, 141, 21))
        font = QtGui.QFont()
        font.setPointSize(12)
        font.setBold(False)
        font.setWeight(50)
        self.label_51.setFont(font)
        self.label_51.setObjectName("label_51")
        self.layoutWidget2 = QtWidgets.QWidget(self.tab_5)
        self.layoutWidget2.setGeometry(QtCore.QRect(60, 80, 164, 171))
        self.layoutWidget2.setObjectName("layoutWidget2")
        self.verticalLayout_2 = QtWidgets.QVBoxLayout(self.layoutWidget2)
        self.verticalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_2.setObjectName("verticalLayout_2")
        self.label_52 = QtWidgets.QLabel(self.layoutWidget2)
        font = QtGui.QFont()
        font.setPointSize(12)
        font.setBold(False)
        font.setWeight(50)
        self.label_52.setFont(font)
        self.label_52.setAlignment(QtCore.Qt.AlignCenter)
        self.label_52.setObjectName("label_52")
        self.verticalLayout_2.addWidget(self.label_52)
        self.btn_back111_5 = QtWidgets.QPushButton(self.layoutWidget2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btn_back111_5.sizePolicy().hasHeightForWidth())
        self.btn_back111_5.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(15)
        font.setBold(False)
        font.setWeight(50)
        self.btn_back111_5.setFont(font)
        self.btn_back111_5.setObjectName("btn_back111_5")
        self.verticalLayout_2.addWidget(self.btn_back111_5)
        self.btn_back111_6 = QtWidgets.QPushButton(self.layoutWidget2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btn_back111_6.sizePolicy().hasHeightForWidth())
        self.btn_back111_6.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(15)
        font.setBold(False)
        font.setWeight(50)
        self.btn_back111_6.setFont(font)
        self.btn_back111_6.setObjectName("btn_back111_6")
        self.verticalLayout_2.addWidget(self.btn_back111_6)
        self.btn_back111_7 = QtWidgets.QPushButton(self.layoutWidget2)
        sizePolicy = QtWidgets.QSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.btn_back111_7.sizePolicy().hasHeightForWidth())
        self.btn_back111_7.setSizePolicy(sizePolicy)
        font = QtGui.QFont()
        font.setPointSize(15)
        font.setBold(False)
        font.setWeight(50)
        self.btn_back111_7.setFont(font)
        self.btn_back111_7.setObjectName("btn_back111_7")
        self.verticalLayout_2.addWidget(self.btn_back111_7)
        self.verticalLayoutWidget_5 = QtWidgets.QWidget(self.tab_5)
        self.verticalLayoutWidget_5.setGeometry(QtCore.QRect(970, 0, 741, 641))
        self.verticalLayoutWidget_5.setObjectName("verticalLayoutWidget_5")
        self.verticalLayout_7 = QtWidgets.QVBoxLayout(self.verticalLayoutWidget_5)
        self.verticalLayout_7.setContentsMargins(0, 0, 0, 0)
        self.verticalLayout_7.setObjectName("verticalLayout_7")
        self.graphicsView_6 = QtWidgets.QGraphicsView(self.verticalLayoutWidget_5)
        self.graphicsView_6.setObjectName("graphicsView_6")
        self.verticalLayout_7.addWidget(self.graphicsView_6)
        self.label_57 = QtWidgets.QLabel(self.verticalLayoutWidget_5)
        self.label_57.setObjectName("label_57")
        self.verticalLayout_7.addWidget(self.label_57, 0, QtCore.Qt.AlignHCenter)
        self.tabWidget.addTab(self.tab_5, "")
        self.gridLayout_11.addWidget(self.tabWidget, 2, 0, 1, 4)
        MainWindow.setCentralWidget(self.centralwidget)
        self.menubar = QtWidgets.QMenuBar(MainWindow)
        self.menubar.setGeometry(QtCore.QRect(0, 0, 1920, 26))
        self.menubar.setObjectName("menubar")
        MainWindow.setMenuBar(self.menubar)
        self.statusbar = QtWidgets.QStatusBar(MainWindow)
        self.statusbar.setObjectName("statusbar")
        MainWindow.setStatusBar(self.statusbar)

        self.retranslateUi(MainWindow)
        self.tabWidget.setCurrentIndex(0)
        QtCore.QMetaObject.connectSlotsByName(MainWindow)

    def retranslateUi(self, MainWindow):
        _translate = QtCore.QCoreApplication.translate
        MainWindow.setWindowTitle(_translate("MainWindow", "MainWindow"))
        self.label_2.setText(_translate("MainWindow", "试块加工——槽铣"))
        self.label.setText(_translate("MainWindow", "重载机器人上位机控制软件"))
        self.pushButton_7.setText(_translate("MainWindow", "报警消除"))
        self.pushButton_5.setText(_translate("MainWindow", "手动模式"))
        self.pushButton_3.setText(_translate("MainWindow", "系统报警"))
        self.pushButton_2.setText(_translate("MainWindow", "系统急停"))
        self.pushButton_6.setText(_translate("MainWindow", "自动模式"))
        self.pushButton.setText(_translate("MainWindow", "机器人通讯"))
        self.textBrowser.setHtml(_translate("MainWindow", "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n"
"<html><head><meta name=\"qrichtext\" content=\"1\" /><style type=\"text/css\">\n"
"p, li { white-space: pre-wrap; }\n"
"</style></head><body style=\" font-family:\'SimSun\'; font-size:15pt; font-weight:400; font-style:normal;\">\n"
"<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><span style=\" font-size:16pt;\">故障信息：</span></p>\n"
"<p style=\"-qt-paragraph-type:empty; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px; font-size:16pt;\"><br /></p></body></html>"))
        self.label_3.setText(_translate("MainWindow", "工件材料："))
        self.comboBox.setItemText(0, _translate("MainWindow", "铝合金"))
        self.comboBox.setItemText(1, _translate("MainWindow", "钛合金"))
        self.comboBox.setItemText(2, _translate("MainWindow", "钢"))
        self.comboBox.setItemText(3, _translate("MainWindow", "碳纤维复合材料"))
        self.comboBox.setItemText(4, _translate("MainWindow", "其他材料"))
        self.label_24.setText(_translate("MainWindow", "mm"))
        self.label_5.setText(_translate("MainWindow", "工件长度:"))
        self.label_6.setText(_translate("MainWindow", "工件高度:"))
        self.label_23.setText(_translate("MainWindow", "mm"))
        self.label_25.setText(_translate("MainWindow", "mm"))
        self.label_4.setText(_translate("MainWindow", "工件宽度:"))
        self.btn_back.setText(_translate("MainWindow", "主界面"))
        self.pushButton_8.setText(_translate("MainWindow", "下一步"))
        self.pushButton_9.setText(_translate("MainWindow", "开始加工"))
        self.label_59.setText(_translate("MainWindow", "工件尺寸示意图"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab), _translate("MainWindow", "第一步"))
        self.pushButton_10.setText(_translate("MainWindow", "开始加工"))
        self.btn_back111.setText(_translate("MainWindow", "主界面"))
        self.pushButton_12.setText(_translate("MainWindow", "下一步"))
        self.pushButton_13.setText(_translate("MainWindow", "上一步"))
        self.label_13.setText(_translate("MainWindow", "刀具编号"))
        self.pushButton_14.setText(_translate("MainWindow", "从刀库选择"))
        self.pushButton_15.setText(_translate("MainWindow", "从文件导入"))
        self.pushButton_16.setText(_translate("MainWindow", "刀具库查看"))
        self.label_55.setText(_translate("MainWindow", "铣刀尺寸示意图"))
        self.label_10.setText(_translate("MainWindow", "齿数Z:"))
        self.label_31.setText(_translate("MainWindow", "mm"))
        self.radioButton_2.setText(_translate("MainWindow", "左旋"))
        self.radioButton.setText(_translate("MainWindow", "右旋"))
        self.label_11.setText(_translate("MainWindow", "旋向:"))
        self.label_12.setText(_translate("MainWindow", "切宽:"))
        self.label_30.setText(_translate("MainWindow", "mm"))
        self.label_28.setText(_translate("MainWindow", "刀具刃径:"))
        self.label_29.setText(_translate("MainWindow", "mm"))
        self.label_32.setText(_translate("MainWindow", "mm"))
        self.label_27.setText(_translate("MainWindow", "刃长:"))
        self.label_26.setText(_translate("MainWindow", "刀具总长:"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_2), _translate("MainWindow", "第二步"))
        self.pushButton_17.setText(_translate("MainWindow", "上一步"))
        self.btn_back111_2.setText(_translate("MainWindow", "主界面"))
        self.pushButton_18.setText(_translate("MainWindow", "下一步"))
        self.pushButton_11.setText(_translate("MainWindow", "开始加工"))
        self.label_16.setText(_translate("MainWindow", "铣削方向:"))
        self.radioButton_5.setText(_translate("MainWindow", "单向"))
        self.radioButton_6.setText(_translate("MainWindow", "往复"))
        self.label_17.setText(_translate("MainWindow", "铣削深度:"))
        self.label_15.setText(_translate("MainWindow", "铣削路径:"))
        self.radioButton_7.setText(_translate("MainWindow", "顺铣"))
        self.radioButton_8.setText(_translate("MainWindow", "逆铣"))
        self.label_33.setText(_translate("MainWindow", "mm"))
        self.label_20.setText(_translate("MainWindow", "安全距离:"))
        self.label_18.setText(_translate("MainWindow", "铣削方式:"))
        self.label_19.setText(_translate("MainWindow", "安全高度:"))
        self.radioButton_9.setText(_translate("MainWindow", "传统"))
        self.radioButton_11.setText(_translate("MainWindow", "螺旋铣"))
        self.radioButton_10.setText(_translate("MainWindow", "其它"))
        self.label_34.setText(_translate("MainWindow", "mm"))
        self.label_35.setText(_translate("MainWindow", "mm"))
        self.label_54.setText(_translate("MainWindow", "槽铣加工示意图"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_3), _translate("MainWindow", "第三步"))
        self.pushButton_19.setText(_translate("MainWindow", "上一步"))
        self.btn_back111_3.setText(_translate("MainWindow", "主界面"))
        self.pushButton_20.setText(_translate("MainWindow", "下一步"))
        self.pushButton_21.setText(_translate("MainWindow", "开始加工"))
        self.label_36.setText(_translate("MainWindow", "mm/min"))
        self.label_8.setText(_translate("MainWindow", "TCP:"))
        self.label_37.setText(_translate("MainWindow", "m/min"))
        self.label_7.setText(_translate("MainWindow", "线速度:"))
        self.label_9.setText(_translate("MainWindow", "每齿进给:"))
        self.label_58.setText(_translate("MainWindow", "mm"))
        self.label_14.setText(_translate("MainWindow", "转速:"))
        self.label_40.setText(_translate("MainWindow", "mm"))
        self.label_22.setText(_translate("MainWindow", "进给速度:"))
        self.label_21.setText(_translate("MainWindow", "切深:"))
        self.label_41.setText(_translate("MainWindow", "切宽:"))
        self.label_38.setText(_translate("MainWindow", "mm/min"))
        self.label_39.setText(_translate("MainWindow", "r/min"))
        self.label_42.setText(_translate("MainWindow", "mm"))
        self.label_56.setText(_translate("MainWindow", "槽铣加工示意图"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_4), _translate("MainWindow", "第四步"))
        self.pushButton_22.setText(_translate("MainWindow", "开始加工"))
        self.pushButton_23.setText(_translate("MainWindow", "下一步"))
        self.label_43.setText(_translate("MainWindow", "主轴功率:"))
        self.label_45.setText(_translate("MainWindow", "实时转速："))
        self.label_49.setText(_translate("MainWindow", "KW"))
        self.label_48.setText(_translate("MainWindow", "RPM"))
        self.btn_back111_4.setText(_translate("MainWindow", "主界面"))
        self.pushButton_24.setText(_translate("MainWindow", "上一步"))
        self.label_50.setText(_translate("MainWindow", "NM"))
        self.label_44.setText(_translate("MainWindow", "RPM"))
        self.label_47.setText(_translate("MainWindow", "主轴扭矩:"))
        self.label_46.setText(_translate("MainWindow", "进给速度:"))
        self.label_51.setText(_translate("MainWindow", "加工监控系统"))
        self.label_52.setText(_translate("MainWindow", "智能刀柄系统"))
        self.btn_back111_5.setText(_translate("MainWindow", "连接确认"))
        self.btn_back111_6.setText(_translate("MainWindow", "系统启动"))
        self.btn_back111_7.setText(_translate("MainWindow", "数据查看"))
        self.label_57.setText(_translate("MainWindow", "加工监控系统"))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_5), _translate("MainWindow", "加工监控"))
import codes_rc
