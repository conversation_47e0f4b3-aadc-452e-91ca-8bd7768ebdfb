from PyQt5.QtWidgets import (
    QApplication, QWidget, QGroupBox, QGridLayout, QLabel, QComboBox,
    QLineEdit, QCheckBox, QPushButton, QSpacerItem, QSizePolicy, QHBoxLayout, QVBoxLayout
)


class step3_ui(QWidget):
    def __init__(self,parent=None):
        super().__init__(parent)

        self.layout_=QHBoxLayout(self)
        self.left_widget = QWidget(self)
        self.left_layout = QVBoxLayout(self.left_widget)
        self.layout_.addWidget(self.left_widget, 4)

        self.drill_parameter_widget=QGroupBox("钻孔工艺参数",self.left_widget)
        self.drill_parameter_layout=QGridLayout(self.drill_parameter_widget)
        self.left_layout.addWidget(self.drill_parameter_widget,3)

        self.drill_parameter_layout.addWidget(QLabel("板材型号"),0,0)
        self.drill_parameter_layout.addWidget(QLabel("主轴转速（r/min）"), 1, 0)
        self.drill_parameter_layout.addWidget(QLabel("进给速度（mm/r）"), 2, 0)
        self.drill_parameter_layout.addWidget(QLabel("进给速度（mm/min）"), 3, 0)
        self.drill_parameter_layout.addWidget(QLabel("锪窝主轴转速（rpm）"),4,0)
        self.drill_parameter_layout.addWidget(QLabel("锪窝深度（mm）"), 5, 0)


        self.drill_parameter_layout.addWidget(QLabel("锪窝进给速度（mm/r）"), 1, 3)
        self.drill_parameter_layout.addWidget(QLabel("锪窝进给速度（mm/min）"), 2, 3)
        self.drill_parameter_layout.addWidget(QLabel("压力脚压力"), 3, 3)
        self.drill_parameter_layout.addWidget(QLabel("第一层板厚度（mm）"), 4, 3)

        self.min_lub_check=QCheckBox("微量润滑")
        self.peck_drill_check=QCheckBox("是否啄钻")
        self.socket_check=QCheckBox("是否锪窝")
        self.drill_parameter_layout.addWidget(self.min_lub_check, 0, 3)
        self.drill_parameter_layout.addWidget(self.peck_drill_check, 0, 4)
        self.drill_parameter_layout.addWidget(self.socket_check, 0, 5)

        self.choose_materials_combobox=QComboBox(self.drill_parameter_widget)
        self.drill_parameter_layout.addWidget(self.choose_materials_combobox, 0, 1,1,2)

        self.mainshaft_speed= QLineEdit(self.drill_parameter_widget)
        self.saved_mainshaft_speed = QLineEdit(self.drill_parameter_widget)

        self.feed_speed= QLineEdit(self.drill_parameter_widget)
        self.saved_feed_speed = QLineEdit(self.drill_parameter_widget)

        self.calculate_feed_speed = QLineEdit(self.drill_parameter_widget)

        self.socket_mainshaft_speed=QLineEdit(self.drill_parameter_widget)
        self.saved_socket_mainshaft_speed = QLineEdit(self.drill_parameter_widget)

        self.socket_deep=QLineEdit(self.drill_parameter_widget)
        self.saved_socket_deep = QLineEdit(self.drill_parameter_widget)

        self.socket_feed_speed=QLineEdit(self.drill_parameter_widget)
        self.saved_socket_feed_speed = QLineEdit(self.drill_parameter_widget)

        self.calculate_socket_feed_speed = QLineEdit(self.drill_parameter_widget)

        self.pressure_foot=QLineEdit(self.drill_parameter_widget)
        self.saved_pressure_foot = QLineEdit(self.drill_parameter_widget)

        self.first_plate_thick= QLineEdit(self.drill_parameter_widget)
        self.saved_first_plate_thick = QLineEdit(self.drill_parameter_widget)

        for text in {self.saved_socket_feed_speed,self.saved_feed_speed,
                     self.saved_socket_deep,self.saved_pressure_foot,
                     self.saved_mainshaft_speed,self.saved_socket_mainshaft_speed,
                     self.saved_first_plate_thick}:
            text.setReadOnly(True)
            text.setStyleSheet("background-color: #f5f5f5;")

        self.read_plc_parameter=QPushButton("读取当前PLC参数",self.drill_parameter_widget)
        self.save_parameter = QPushButton("确认参数", self.drill_parameter_widget)

        self.drill_parameter_layout.addWidget(self.mainshaft_speed, 1, 1, 1, 1)
        self.drill_parameter_layout.addWidget(self.saved_mainshaft_speed, 1, 2, 1, 1)
        self.drill_parameter_layout.addWidget(self.feed_speed, 2, 1, 1, 1)
        self.drill_parameter_layout.addWidget(self.saved_feed_speed, 2, 2, 1, 1)
        self.drill_parameter_layout.addWidget(self.calculate_feed_speed, 3, 1, 1, 1)
        self.drill_parameter_layout.addWidget(self.socket_mainshaft_speed, 4, 1, 1, 1)
        self.drill_parameter_layout.addWidget(self.saved_socket_mainshaft_speed, 4, 2, 1, 1)
        self.drill_parameter_layout.addWidget(self.socket_deep, 5, 1, 1, 1)
        self.drill_parameter_layout.addWidget(self.saved_socket_deep, 5, 2, 1, 1)

        self.drill_parameter_layout.addWidget(self.socket_feed_speed, 1, 4, 1, 1)
        self.drill_parameter_layout.addWidget(self.saved_socket_feed_speed, 1, 5, 1, 1)
        self.drill_parameter_layout.addWidget(self.calculate_socket_feed_speed, 2, 4, 1, 1)
        self.drill_parameter_layout.addWidget(self.pressure_foot, 3, 4, 1, 1)
        self.drill_parameter_layout.addWidget(self.saved_pressure_foot, 3, 5, 1, 1)
        self.drill_parameter_layout.addWidget(self.first_plate_thick, 4, 4, 1, 1)
        self.drill_parameter_layout.addWidget(self.saved_first_plate_thick, 4, 5, 1, 1)

        self.drill_parameter_layout.addWidget(self.read_plc_parameter, 5, 3, 1, 2)
        self.drill_parameter_layout.addWidget(self.save_parameter, 5, 5, 1, 1)



        self.right_widget = QWidget(self)
        self.right_layout=QVBoxLayout(self.right_widget)
        self.layout_.addWidget(self.right_widget,2)

