import socket
import struct
import numpy as np
import tkinter as tk
import threading
import time
import queue

# 创建socket对象
socket_client = socket.socket()
# 连接到服务器
socket_client.connect(("localhost", 8099))


def SendInstructions(num1, num2, vice_num):
    msgChar = ['Z', 'G', 'M', 'E', 'D', 'I', 'C', 'A', 'L', '_', 'M', 'S', 'G', 0, num1, 0, 0, 0, num2]
    if vice_num != 0:
        msgChar.append(vice_num)
    # # 创建字节数组
    byte_array = bytearray()
    for item in msgChar:
        if isinstance(item, str):  # 对于字符
            byte_array.append(ord(item))  # 将字符转换为对应 ASCII 值
        elif isinstance(item, int):  # 对于整数
            byte_array.append(item)  # 直接添加整数
    return byte_array


def rotation_matrix_to_quaternion(tempRT):
    # 假设 tempRT 是一个包含的 double 的数组
    # 取出第2到第10个元素，并假设这些元素组成一个 3x3 的旋转矩阵
    # 注意：Python 索引从 0 开始，这里提取的是索引为 1 到 9 的元素
    R = np.array([
        [tempRT[0], tempRT[1], tempRT[2]],  # 第一行
        [tempRT[3], tempRT[4], tempRT[5]],  # 第二行
        [tempRT[6], tempRT[7], tempRT[8]],  # 第三行
    ])

    q = np.zeros(4)
    trace = R[0, 0] + R[1, 1] + R[2, 2]

    if trace > 0:
        s = np.sqrt(trace + 1.0) * 2  # s=4*Q0
        q[0] = 0.25 * s  # Q0
        q[1] = (R[2, 1] - R[1, 2]) / s  # Qx
        q[2] = (R[0, 2] - R[2, 0]) / s  # Qy
        q[3] = (R[1, 0] - R[0, 1]) / s  # Qz
    else:
        print('error')

    return q


new_point = [0.00, 0.00, 0.00]
transformation_matrix = [[-5.14751643e-02, -1.54612443e-03, 9.98673078e-01, 2.00453274e+03],
                         [9.97727942e-01, -4.36029059e-02, 5.13589435e-02, 1.94343926e+02],
                         [4.34656409e-02, 9.99047745e-01, 3.78707829e-03, 6.58182770e+02],
                         [0.00000000e+00, 0.00000000e+00, 0.00000000e+00, 1.00000000e+00]]


def transform_point(transformation_matrix, point):
    # 将点转换为齐次坐标
    point_homogeneous = np.array([point[0], point[1], point[2], 1])

    # 进行坐标变换
    transformed_point = np.dot(transformation_matrix, point_homogeneous)

    # 提取变换后的坐标（去掉齐次坐标的最后一位）
    return transformed_point[:3]


Msg_LinkDevice = SendInstructions(80, 1, 0)
Msg_AddTool = SendInstructions(100, 1, 0)
Msg_Track = SendInstructions(101, 1, 0)
MSG_HEAD = b'ZGMEDICAL_MSG'  # 您的消息头定义
COMMAND_MIN = 1  # 根据需求设置最小命令长度
msg = None
socket_client.send(Msg_LinkDevice)
socket_client.send(Msg_Track)
data_queue = queue.Queue()


def perform_other_tasks():
    while True:
        # time.sleep(0.5)
        # 接受消息
        recv_data = socket_client.recv(4096)  # 1024是缓冲区大小，一般就填1024， recv是阻塞式
        if len(recv_data) == 167:
            rms_bytes = recv_data[43:147]
            double_values = []
            # 每次解析 8 个字节
            for i in range(0, len(rms_bytes), 8):
                # 获取当前 8 字节片段
                byte_segment = rms_bytes[i:i + 8]
                # 确保该片段刚好有 8 个字节
                if len(byte_segment) == 8:
                    # 解析为 double 并存储
                    double_value = struct.unpack('d', byte_segment)[0]
                    double_values.append(double_value)
                else:
                    print(f"数据不足以形成完整的 double: {byte_segment}")
            # 打印解析后的 double 值
            # print("解析得到的 double 值:", double_values)
            # 计算并输出角度
            # q = rotation_matrix_to_quaternion(double_values[1:10])
            # print("解析得到的坐标值：")
            # print(
            #     f"RMS:{double_values[0]:.2f};Tx: {double_values[10]:.2f};Ty: {double_values[11]:.2f};Tz: {double_values[12]:.2f}")
            # print(f"Q0:{q[0]:.2f};Qx: {q[1]:.2f};Qy: {q[2]:.2f};Qz: {q[3]:.2f}")
            point = [double_values[10], double_values[11], double_values[12]]
            new_point = transform_point(transformation_matrix, point)
            formatted_point = [f"{new_point[0]:.2f}", f"{new_point[1]:.2f}", f"{new_point[2]:.2f}"]
            data_queue.put(formatted_point)
            # label.config(text=f"解析得到的坐标值: {new_point}")
            # print("解析得到的坐标值:", new_point)
        else:
            print("消息格式不正确。")


# 启动其他任务的线程
other_thread = threading.Thread(target=perform_other_tasks)
other_thread.daemon = True  # 使线程在主程序退出时自动结束
other_thread.start()
# 创建主窗口
root = tk.Tk()
root.title("实时坐标值更新")
# 标签用来显示坐标值
label = tk.Label(root, text="解析得到的坐标值:", font=("Helvetica", 16))
label.pack(pady=20)


def update_label():
    while True:
        try:
            # 从队列中获取新的坐标值
            new_point = data_queue.get(timeout=1)  # 等待1秒获取数据
            label.config(text=f"解析得到的坐标值: {new_point}")
        except queue.Empty:
            pass  # 如果队列为空，则继续等待新的数据


thread2 = threading.Thread(target=update_label)
thread2.daemon = True  # 使线程在主程序退出时自动结束
thread2.start()
root.mainloop()


def parse_message(data):
    if len(data) < COMMAND_MIN:
        return

        # 检查消息头
    if not data.startswith(MSG_HEAD):
        print("Server recv invalid head")
        return

        # 记录消息的位置
    all_pos = []
    pos = 0
    while pos < len(data):
        find_pos = data.find(MSG_HEAD, pos)
        if find_pos != -1:
            all_pos.append(find_pos)
            pos = find_pos + 1
        else:
            break

    for i in range(len(all_pos)):
        if i + 1 == len(all_pos):
            msg = data[all_pos[i]:]  # 获取最后一条消息
        else:
            msg = data[all_pos[i]:all_pos[i + 1]]  # 获取当前消息

        # 调用用户定义的回调函数
    msg_callback(msg)


def msg_callback(msg):
    print("Received message:", msg.decode())


# 关闭连接
socket_client.close()
