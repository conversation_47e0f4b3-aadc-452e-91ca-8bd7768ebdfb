import socket
import sys
import struct

class KukaEkiClient:
    """
    简易 KUKA EKI 客户端，用于与机器人控制器的 EKI 服务端通讯。
    """

    def __init__(self, host: str, port: int, timeout: float = 5.0):
        self.host = host
        self.port = port
        self.timeout = timeout
        self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.sock.settimeout(self.timeout)

    def connect(self):
        """连接到机器人 EKI 服务端"""
        try:
            self.sock.connect((self.host, self.port))
        except socket.error as e:
            print(f"连接失败: {e}")
            sys.exit(1)

    def send_xml(self, xml_str: str):
        """
        发送 XML 字符串到机器人。
        EKI 默认按文本协议收发，可直接 sendall。
        """
        data = xml_str.encode('utf-8')
        # 如果需要固定长度头，可在此处添加，比如长度前缀：
        # header = struct.pack('!I', len(data))
        # self.sock.sendall(header + data)
        self.sock.sendall(data)

    def receive(self, bufsize: int = 4096) -> str:
        """
        接收机器人应答，将字节解码为字符串返回。
        若使用长度前缀，可先 recv 固定字节再解析长度。
        """
        chunks = []
        while True:
            try:
                chunk = self.sock.recv(bufsize)
                if not chunk:
                    break
                chunks.append(chunk)
                # 如果数据中包含结束标志，可以在此判断并 break
            except socket.timeout:
                break
        return b''.join(chunks).decode('utf-8')

    def close(self):
        """关闭 socket 连接"""
        self.sock.close()


if __name__ == "__main__":
    # 配置与 KUKA 控制器相同的 IP/PORT
    HOST = "*************"
    PORT = 54600

    client = KukaEkiClient(HOST, PORT)
    client.connect()
    print("已连接到 KUKA EKI 服务端")

    # 构造发送给 <RECEIVE> 中定义的 <Ext><Msg> 节点
    xml_to_send = """<Ext><Msg>Hello KUKA!</Msg></Ext>"""
    client.send_xml(xml_to_send)
    print(f"已发送: {xml_to_send}")

    # 接收机器人 <SEND> 中的 <Robot><Ack> 节点应答
    reply = client.receive()
    print(f"收到应答: {reply}")

    client.close()
    print("连接已关闭")
