import sys
from PyQt5 import uic
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QLabel, QPushButton,
    QTextEdit, QLineEdit, QComboBox, QTabWidget, QGroupBox,
    QGridLayout, QHBoxLayout, QVBoxLayout, QStackedWidget, QSizePolicy
)
from PyQt5.QtCore import Qt


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.resize(1920, 1080)

        # 创建堆栈容器
        self.stack = QStackedWidget()
        self.setCentralWidget(self.stack)

        # 动态 load UI，返回 QWidget 实例
        self.page_main = uic.loadUi("重载机器人/主界面.ui")   # 这里 page_main.btn_slot 可用
        self.page_slot = uic.loadUi("重载机器人/槽铣界面.ui")   # 这里 page_slot.btn_back 可用

        # 加入堆栈
        self.stack.addWidget(self.page_main)  # index 0
        self.stack.addWidget(self.page_slot)  # index 1

        # 直接访问属性（uic.loadUi 会把 objectName 变成员属性）
        self.page_main.btn_slot.clicked.connect(lambda: self.stack.setCurrentIndex(1))
        self.page_slot.btn_back.clicked.connect(lambda: self.stack.setCurrentIndex(0))

        # 访问其它控件，比如读取用户在 page_slot 中 le_program 中输入的文本：
        # text = self.page_slot.le_program.text()


if __name__ == "__main__":
    app = QApplication(sys.argv)
    w = MainWindow()
    w.show()
    sys.exit(app.exec_())
