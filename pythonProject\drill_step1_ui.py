from PyQt5.QtGui import QFont
from PyQt5.QtWidgets import (
    QWidget, QPushButton, QSpinBox, QDoubleSpinBox,
    QTextEdit, QLabel, QFormLayout, QHBoxLayout,
    QVBoxLayout, QApplication, QTabWidget, QSizePolicy
)
from config import *
from PyQt5.QtCore import Qt

from opengl_widget import GLGridWidget



class step1_ui(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)

        self.layout_ = QHBoxLayout(self)

        self.left_widget = QWidget(self)
        self.left_layout=QHBoxLayout(self.left_widget)
        self.layout_.addWidget(self.left_widget,4)
        

        self.left_grid_widget = GLGridWidget(self)
        self.left_layout.addWidget(self.left_grid_widget,8)

        self.option_widget = QWidget(self)
        self.option_layout=QVBoxLayout(self.option_widget)
        self.left_layout.addWidget(self.option_widget,2)

        self.restore_buttorn = QPushButton("恢复初始选择")
        self.option_layout.addWidget(self.restore_buttorn,1)

        self.right_select_widget = QWidget(self.option_widget)
        self.right_select_layout = QHBoxLayout(self.right_select_widget)
        self.select_all_buttorn = QPushButton("全选")
        self.select_inv_buttorn = QPushButton("反选")
        self.right_select_layout.addWidget(self.select_all_buttorn)
        self.right_select_layout.addWidget(self.select_inv_buttorn)
        self.option_layout.addWidget(self.right_select_widget,1)

        # Parameter form
        self.right_form_widget = QWidget(self.option_widget)
        self.right_form_layout = QFormLayout(self.right_form_widget)
        self.spin_rows = QSpinBox(); self.spin_rows.setRange(0, 50); self.spin_rows.setValue(DEFAULT_ROWS)
        self.spin_cols = QSpinBox(); self.spin_cols.setRange(0, 50); self.spin_cols.setValue(DEFAULT_COLS)
        self.spin_dw = QDoubleSpinBox(); self.spin_dw.setRange(0, DISPLAY_PLATE_W); self.spin_dw.setValue(DEFAULT_DRILL_W)
        self.spin_dh = QDoubleSpinBox(); self.spin_dh.setRange(0, DISPLAY_PLATE_H); self.spin_dh.setValue(DEFAULT_DRILL_H)
        self.spin_hs = QDoubleSpinBox(); self.spin_hs.setRange(0, 1000); self.spin_hs.setValue(DEFAULT_SPACING)
        self.spin_vs = QDoubleSpinBox(); self.spin_vs.setRange(0, 1000); self.spin_vs.setValue(DEFAULT_SPACING)
        self.spin_dia = QDoubleSpinBox(); self.spin_dia.setRange(0, 500); self.spin_dia.setValue(DEFAULT_DIAMETER)
        for label, widget in [
            ("行数", self.spin_rows), ("列数", self.spin_cols),
            ("钻孔区域宽度", self.spin_dw), ("钻孔区域高度", self.spin_dh),
            ("孔间距", self.spin_hs), ("孔排距", self.spin_vs), ("孔直径", self.spin_dia)
        ]:
            self.right_form_layout.addRow(label, widget)
        self.option_layout.addWidget(self.right_form_widget,7)

        self.right_select2_widget = QWidget(self.option_widget)
        self.right_select2_layout = QHBoxLayout(self.right_select2_widget)
        self.select_show_hole = QPushButton("生成孔位")
        self.select_save = QPushButton("保存")
        self.right_select2_layout.addWidget(self.select_show_hole)
        self.right_select2_layout.addWidget(self.select_save)
        self.option_layout.addWidget(self.right_select2_widget,1)


        self.right_widget = QWidget(self)
        self.right_layout=QVBoxLayout(self.right_widget)
        self.layout_.addWidget(self.right_widget,2)
