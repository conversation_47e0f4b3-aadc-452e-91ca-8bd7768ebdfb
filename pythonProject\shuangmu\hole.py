import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext


class HoleGeneratorApp:
    def __init__(self, master):
        self.master = master
        master.title("阵列孔坐标生成器")
        master.geometry("800x600")

        # 输入参数设置
        self.create_input_fields()

        # 结果展示区域
        self.create_result_area()

        # 生成按钮
        self.generate_btn = ttk.Button(master, text="生成孔坐标", command=self.generate_holes)
        self.generate_btn.grid(row=6, column=0, columnspan=2, pady=10)

    def create_input_fields(self):
        # 输入参数标签和输入框
        inputs = [
            ("孔行数", "rows", 3),
            ("孔列数", "cols", 4),
            ("行间距(mm)", "row_spacing", 100),
            ("列间距(mm)", "col_spacing", 150),
            ("上边距(mm)", "top_margin", 50),
            ("左边距(mm)", "left_margin", 50)
        ]

        self.entries = {}
        for i, (label_text, var_name, default) in enumerate(inputs):
            ttk.Label(self.master, text=label_text).grid(row=i, column=0, padx=10, pady=5, sticky=tk.W)
            entry = ttk.Entry(self.master)
            entry.insert(0, str(default))
            entry.grid(row=i, column=1, padx=10, pady=5, sticky=tk.EW)
            self.entries[var_name] = entry

    def create_result_area(self):
        # 结果显示文本框
        self.result_text = scrolledtext.ScrolledText(self.master, width=60, height=20)
        self.result_text.grid(row=7, column=0, columnspan=2, padx=10, pady=10, sticky=tk.NSEW)

        # 画布用于示意图展示
        self.canvas = tk.Canvas(self.master, width=800, height=400, bg="white")
        self.canvas.grid(row=8, column=0, columnspan=2, padx=10, pady=10)

    def validate_inputs(self):
        try:
            params = {
                "rows": int(self.entries["rows"].get()),
                "cols": int(self.entries["cols"].get()),
                "row_spacing": float(self.entries["row_spacing"].get()),
                "col_spacing": float(self.entries["col_spacing"].get()),
                "top_margin": float(self.entries["top_margin"].get()),
                "left_margin": float(self.entries["left_margin"].get())
            }

            if params["rows"] < 1 or params["cols"] < 1:
                raise ValueError("行数和列数必须大于0")

            return params
        except ValueError as e:
            messagebox.showerror("输入错误", f"无效输入: {str(e)}")
            return None

    def generate_holes(self):
        params = self.validate_inputs()
        if not params:
            return

        # 计算孔坐标
        holes = []
        for row in range(params["rows"]):
            for col in range(params["cols"]):
                x = params["left_margin"] + col * params["col_spacing"]
                y = params["top_margin"] + row * params["row_spacing"]
                holes.append((x, y))

        # 更新文本显示
        self.result_text.delete(1.0, tk.END)
        for i, (x, y) in enumerate(holes):
            self.result_text.insert(tk.END, f"孔 {i + 1:02d}: X={x:.2f}mm, Y={y:.2f}mm\n")

        # 更新示意图
        self.draw_holes(holes)

    def draw_holes(self, holes):
        self.canvas.delete("all")
        scale = 0.5  # 缩放比例用于显示

        # 绘制板轮廓
        self.canvas.create_rectangle(
            0, 0,
            800 * scale, 600 * scale,
            outline="black"
        )

        # 绘制所有孔（用红色圆圈表示）
        for x, y in holes:
            scaled_x = x * scale
            scaled_y = y * scale
            self.canvas.create_oval(
                scaled_x - 5, scaled_y - 5,
                scaled_x + 5, scaled_y + 5,
                fill="red", outline="red"
            )


if __name__ == "__main__":
    root = tk.Tk()
    app = HoleGeneratorApp(root)
    root.mainloop()