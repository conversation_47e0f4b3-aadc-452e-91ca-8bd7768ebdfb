# drill_ui.py
# -*- coding: utf-8 -*-
from PyQt5.QtGui import QFont
from PyQt5.QtWidgets import (
    QWidget, QPushButton, QSpinBox, QDoubleSpinBox,
    QTextEdit, QLabel, QFormLayout, QHBoxLayout,
    QVBoxLayout, QApplication, QTabWidget, QSizePolicy, QGridLayout, QGroupBox
)
from PyQt5.QtCore import Qt

from main_ui import clear_all_margins
# 导入 OpenGL widget
from opengl_widget import GLGridWidget, DISPLAY_PLATE_W, DISPLAY_PLATE_H
from drill_step1_ui import step1_ui
from drill_step2_ui import step2_ui
from drill_step3_ui import step3_ui
from drill_step4_ui import step4_ui

class drill_ui(QWidget):

    def __init__(self, parent=None):
        super().__init__(parent)

        self.layout_=QVBoxLayout(self)

        self.up_widget=QWidget(self)
        self.up_layout=QHBoxLayout(self.up_widget)
        self.layout_.addWidget(self.up_widget,2)

        self.main_option_widget=QWidget(self.up_widget)
        self.main_option_layout=QGridLayout(self.main_option_widget)
        self.up_layout.addWidget(self.main_option_widget,5)

        self.robot_connect_but=QPushButton("机械臂通信")
        self.sys_alarm_but = QPushButton("系统报警")
        self.auto_mode_but = QPushButton("自动模式")
        self.sys_stop_but = QPushButton("系统急停")
        self.alarm_reset_but= QPushButton("报警消除")
        self.manual_mode_but = QPushButton("手动模式")
        self.main_option_layout.addWidget(self.robot_connect_but,0,0)
        self.main_option_layout.addWidget(self.sys_alarm_but,0,1)
        self.main_option_layout.addWidget(self.auto_mode_but,0,2)
        self.main_option_layout.addWidget(self.sys_stop_but,1,0)
        self.main_option_layout.addWidget(self.alarm_reset_but,1,1)
        self.main_option_layout.addWidget(self.manual_mode_but,1,2)


        self.log_widget=QGroupBox("日志",self.up_widget)
        self.log_layout=QVBoxLayout(self.log_widget)
        self.up_layout.addWidget(self.log_widget,5)

        self.log_text = QTextEdit(self.log_widget)
        self.log_text.setReadOnly(True)
        self.log_layout.addWidget(self.log_text)

        self.process_widget=QWidget(self)
        self.process_layout=QVBoxLayout(self.process_widget)
        self.layout_.addWidget(self.process_widget, 8)

        self.process_title_widget=QWidget(self.process_widget)
        self.process_title_layout=QHBoxLayout(self.process_title_widget)
        self.process_layout.addWidget(self.process_title_widget,1)

        self.help_but = QPushButton("帮助文件")
        self.process_title_layout.addWidget(self.help_but,2)
        self.process_lable=QLabel("试块加工——制孔")
        process_lable_font = QFont("Microsoft YaHei", 28)
        self.process_lable.setFont(process_lable_font)
        self.process_title_layout.addWidget(self.process_lable,8, Qt.AlignCenter)

        self.tab1_widget=QTabWidget(self.process_widget)
        self.process_layout.addWidget(self.tab1_widget,12)

        self.tab1_page1_widget=QWidget(self.tab1_widget)
        self.tab1_page1_layout=QVBoxLayout(self.tab1_page1_widget)
        self.tab1_widget.addTab(self.tab1_page1_widget,"第一步")
        self.step1_widget=step1_ui(self.tab1_page1_widget)
        self.tab1_page1_layout.addWidget(self.step1_widget)


        self.tab1_page2_widget = QWidget(self.tab1_widget)
        self.tab1_page2_layout = QVBoxLayout(self.tab1_page2_widget)
        self.tab1_widget.addTab(self.tab1_page2_widget, "第二步")
        self.step2_widget=step2_ui(self.tab1_page2_widget)
        self.tab1_page2_layout.addWidget(self.step2_widget)

        self.tab1_page3_widget = QWidget(self.tab1_widget)
        self.tab1_page3_layout = QVBoxLayout(self.tab1_page3_widget)
        self.tab1_widget.addTab(self.tab1_page3_widget, "第三步")
        self.step3_widget=step3_ui(self.tab1_page3_widget)
        self.tab1_page3_layout.addWidget(self.step3_widget)

        self.tab1_page4_widget = QWidget(self.tab1_widget)
        self.tab1_page4_layout = QVBoxLayout(self.tab1_page4_widget)
        self.tab1_widget.addTab(self.tab1_page4_widget, "第四步")
        self.step4_widget=step4_ui(self.tab1_page4_widget)
        self.tab1_page4_layout.addWidget(self.step4_widget)

        self.tab1_page5_widget = QWidget(self.tab1_widget)
        self.tab1_page5_layout = QVBoxLayout(self.tab1_page5_widget)
        self.tab1_widget.addTab(self.tab1_page5_widget, "加工监控")

        self.process_option_widget=QWidget(self.process_widget)
        self.process_option_layout=QHBoxLayout(self.process_option_widget)
        self.process_layout.addWidget(self.process_option_widget,1)

        self.main_interface_but=QPushButton("主界面")
        self.previous_step_but = QPushButton("上一步")
        self.next_step_but = QPushButton("下一步")
        self.start_process_but = QPushButton("开始加工")
        self.process_option_layout.addWidget(self.main_interface_but,1)
        self.process_option_layout.addWidget(self.previous_step_but, 1)
        self.process_option_layout.addWidget(self.next_step_but, 1)
        self.process_option_layout.addWidget(self.start_process_but, 1)

        # self.NC_widget=QWidget(parent)
        # self.NC_layout=QHBoxLayout(self.NC_widget)
        # self.layout_.addWidget(self.NC_widget,3)
        #
        # self.NC_show_widget=QWidget(self.NC_widget)
        # self.NC_show_layout=QVBoxLayout(self.NC_show_widget)
        # self.NC_layout.addWidget(self.NC_show_widget, 8)
        #
        # self.NC_option_widget=QWidget(self.NC_widget)
        # self.NC_option_layout=QVBoxLayout(self.NC_option_widget)
        # self.NC_layout.addWidget(self.NC_option_widget, 2)

# -------------- 独立运行预览 --------------


if __name__ == "__main__":
    import sys
    app = QApplication(sys.argv)
    font = QFont("Microsoft YaHei", 16)  # 微软雅黑，10 号
    font.setBold(False)
    app.setFont(font)
    # 全局样式：所有 QWidget+子类都带 1px 黑色边框
    # app.setStyleSheet("QWidget { border: 1px solid black;}")

    drill_ui = drill_ui()
    clear_all_margins(drill_ui)
    drill_ui.setWindowTitle("试钻板 UI 预览")
    drill_ui.showMaximized()
    drill_ui.show()
    sys.exit(app.exec_())
