#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import math
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QTextEdit,
    QPushButton, QSpinBox, QDoubleSpinBox,
    QFormLayout, QHBoxLayout, QVBoxLayout,
    QLabel, QSizePolicy, QMessageBox
)
from PyQt5.QtCore import Qt, pyqtSignal, QSize, QThread
from PyQt5.QtOpenGL import QGLWidget
from OpenGL.GL import *  # noqa
from ABB_control import RobotClient
from SiemensModbus import PLC_comm
from config import *
from opengl_widget import *



class SendThread(QThread):
    log = pyqtSignal(str)
    error = pyqtSignal(str)
    processed = pyqtSignal(int, int)

    def __init__(self, holes_cmds: list, parent=None):
        super().__init__(parent)
        self.holes_cmds = holes_cmds  # list of (row, col, cmd)
        self.robot = RobotClient(ROBOT_IP, ROBOT_PORT)

    def run(self):
        try:
            self.robot.connect()
            self.log.emit(f"connect IP {ROBOT_IP} PORT {ROBOT_PORT}\n")
            for i, j, cmd in self.holes_cmds:
                self.log.emit(f"Sent: {cmd}\n")
                resp = self.robot.send_command(cmd)
                # if resp=="0:robot_move_complete":
                #
                self.log.emit(f"Received: {resp}\n")
                if i is not None and j is not None:
                    self.processed.emit(i, j)
            self.robot.close()
        except Exception as e:
            self.error.emit(str(e))

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("试钻板机器人钻孔平台")
        self.resize(1920, 1020)
        central = QWidget()
        main_hbox = QHBoxLayout(central)

        # Left: OpenGL widget
        self.grid = GLGridWidget(self)
        left_panel = QWidget()
        left_panel.setLayout(QVBoxLayout())
        left_panel.layout().addWidget(self.grid)

        # Right: controls
        right_vbox = QVBoxLayout()
        self.btn_connect = QPushButton("连接机械臂")
        self.btn_connect.clicked.connect(self.connect_robot)
        self.btn_connect_1 = QPushButton("连接PLC")
        self.btn_connect_1.clicked.connect(self.connect_PLC)
        btn_restore = QPushButton("恢复默认设置")
        btn_restore.clicked.connect(self.restore_defaults)
        right_vbox.addWidget(self.btn_connect)
        right_vbox.addWidget(self.btn_connect_1)
        right_vbox.addWidget(btn_restore)

        sel_box = QHBoxLayout()
        btn_all = QPushButton("全选")
        btn_all.clicked.connect(self.grid.all_selection)
        btn_inv = QPushButton("反选")
        btn_inv.clicked.connect(self.grid.invert_selection)
        sel_box.addWidget(btn_all)
        sel_box.addWidget(btn_inv)
        right_vbox.addLayout(sel_box)

        # Parameter form
        form = QFormLayout()
        self.spin_rows = QSpinBox(); self.spin_rows.setRange(0, 50); self.spin_rows.setValue(DEFAULT_ROWS)
        self.spin_cols = QSpinBox(); self.spin_cols.setRange(0, 50); self.spin_cols.setValue(DEFAULT_COLS)
        self.spin_dw = QDoubleSpinBox(); self.spin_dw.setRange(0, DISPLAY_PLATE_W); self.spin_dw.setValue(DEFAULT_DRILL_W)
        self.spin_dh = QDoubleSpinBox(); self.spin_dh.setRange(0, DISPLAY_PLATE_H); self.spin_dh.setValue(DEFAULT_DRILL_H)
        self.spin_hs = QDoubleSpinBox(); self.spin_hs.setRange(0, 1000); self.spin_hs.setValue(DEFAULT_SPACING)
        self.spin_vs = QDoubleSpinBox(); self.spin_vs.setRange(0, 1000); self.spin_vs.setValue(DEFAULT_SPACING)
        self.spin_dia = QDoubleSpinBox(); self.spin_dia.setRange(0, 500); self.spin_dia.setValue(DEFAULT_DIAMETER)
        for label, widget in [
            ("行数", self.spin_rows), ("列数", self.spin_cols),
            ("钻孔区域宽度", self.spin_dw), ("钻孔区域高度", self.spin_dh),
            ("孔间距", self.spin_hs), ("孔排距", self.spin_vs), ("孔直径", self.spin_dia)
        ]:
            form.addRow(label, widget)
        right_vbox.addLayout(form)

        # Generate code preview
        btn_gen = QPushButton("生成机械臂代码")
        btn_gen.clicked.connect(self.gen_gcode)
        self.text_preview = QTextEdit(readOnly=True)
        right_vbox.addWidget(btn_gen)
        right_vbox.addWidget(QLabel("预览："))
        right_vbox.addWidget(self.text_preview)

        # Send and log
        btn_send = QPushButton("发送")
        btn_send.clicked.connect(self.send_robot_code)
        self.text_log = QTextEdit(readOnly=True)
        right_vbox.addWidget(btn_send)
        right_vbox.addWidget(QLabel("日志："))
        right_vbox.addWidget(self.text_log)

        right_panel = QWidget()
        right_panel.setLayout(right_vbox)
        main_hbox.addWidget(left_panel, 2)
        main_hbox.addWidget(right_panel, 1)
        self.setCentralWidget(central)

        # Connect parameter changes
        for w in [self.spin_rows, self.spin_cols, self.spin_dw, self.spin_dh, self.spin_hs, self.spin_vs, self.spin_dia]:
            w.valueChanged.connect(self.update_params)

    def connect_robot(self):
        self.robot = RobotClient(ROBOT_IP, ROBOT_PORT)
        robot_connect=self.robot.connect()
        if not robot_connect:
            QMessageBox.information(self, "提示", "机械臂连接失败")
            self.btn_connect.setStyleSheet("background-color : red")
        else:
            self.btn_connect.setStyleSheet("background-color : green")
            self.robot.send_command("exit")
        self.robot.close()


    def connect_PLC(self):
        self.PLC = PLC_comm('***********')
        PLC_connect=self.PLC.connect()
        if not PLC_connect:
            QMessageBox.information(self, "提示", "PLC连接失败")
            self.btn_connect_1.setStyleSheet("background-color : red")
        else:
            self.btn_connect_1.setStyleSheet("background-color : green")

    def restore_defaults(self):
        self.spin_rows.setValue(DEFAULT_ROWS)
        self.spin_cols.setValue(DEFAULT_COLS)
        self.spin_dw.setValue(DEFAULT_DRILL_W)
        self.spin_dh.setValue(DEFAULT_DRILL_H)
        self.spin_hs.setValue(DEFAULT_SPACING)
        self.spin_vs.setValue(DEFAULT_SPACING)
        self.spin_dia.setValue(DEFAULT_DIAMETER)
        self.grid.restore_defaults()

    def update_params(self):
        self.grid.set_parameters(
            self.spin_rows.value(), self.spin_cols.value(),
            self.spin_dw.value(), self.spin_dh.value(),
            self.spin_hs.value(), self.spin_vs.value(),
            self.spin_dia.value()
        )

    def gen_gcode(self):
        cmds, previews = [], []
        cx, cy = DISPLAY_PLATE_W/2, DISPLAY_PLATE_H/2
        offx = -((self.grid.cols-1)/2)*self.spin_hs.value()
        offy = -((self.grid.rows-1)/2)*self.spin_vs.value()
        quat = (1,0,0,0)
        for i in range(self.grid.rows):
            for j in range(self.grid.cols):
                if self.grid.selected[i][j]:
                    x = cx + offx + j*self.spin_hs.value()
                    y = cy + offy + i*self.spin_vs.value()
                    z = -5*self.spin_dia.value()
                    previews.append(f"X{x:.3f} Y{y:.3f} Z{z:.3f}\n")
                    cmds.append(f"drill:50:tool0:wobj1;{x:.3f},{y:.3f},{z:.3f},{quat[0]},{quat[1]},{quat[2]},{quat[3]};")
        if not previews:
            previews = ["(No holes selected)\n"]
        cmds.append("exit")
        self.text_preview.setPlainText(''.join(previews))
        self.robot_cmds = cmds

    def send_robot_code(self):
        if not getattr(self, 'robot_cmds', None):
            QMessageBox.information(self, "提示", "请先生成命令。")
            return
        holes_cmds = []
        cx, cy = DISPLAY_PLATE_W/2, DISPLAY_PLATE_H/2
        offx = -((self.grid.cols-1)/2)*self.spin_hs.value()
        offy = -((self.grid.rows-1)/2)*self.spin_vs.value()
        quat = (1,0,0,0)
        for i in range(self.grid.rows):
            for j in range(self.grid.cols):
                if self.grid.selected[i][j]:
                    x = cx + offx + j*self.spin_hs.value()
                    y = cy + offy + i*self.spin_vs.value()
                    z = -5*self.spin_dia.value()
                    cmd = f"drill:50:tool0:wobj1;{x:.3f},{y:.3f},{z:.3f},{quat[0]},{quat[1]},{quat[2]},{quat[3]};"
                    holes_cmds.append((i, j, cmd))
        holes_cmds.append((None, None, "exit"))
        self.thread = SendThread(holes_cmds)
        self.thread.log.connect(lambda msg: self.text_log.insertPlainText(msg))
        self.thread.processed.connect(self.grid.mark_processed)
        self.thread.error.connect(lambda e: QMessageBox.critical(self, "发送失败", e))
        self.thread.start()


if __name__ == "__main__":
    app = QApplication(sys.argv)
    win = MainWindow()
    win.show()
    sys.exit(app.exec_())
