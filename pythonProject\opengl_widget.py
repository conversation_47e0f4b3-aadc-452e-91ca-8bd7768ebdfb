import math
from PyQt5.QtWidgets import (QSizePolicy, QMessageBox)
from PyQt5.QtCore import Qt, pyqtSignal, QSize, QThread
from PyQt5.QtOpenGL import QGLWidget
from OpenGL.GL import *  # noqa
from config import *

class GLGridWidget(QGLWidget):
    cellClicked = pyqtSignal(int, int)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_params()
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

    def init_params(self):
        self.rows = DEFAULT_ROWS
        self.cols = DEFAULT_COLS
        self.drill_w = DEFAULT_DRILL_W
        self.drill_h = DEFAULT_DRILL_H
        self.h_spacing = DEFAULT_SPACING
        self.v_spacing = DEFAULT_SPACING
        self.dia = DEFAULT_DIAMETER
        self.reset_matrices()

    def reset_matrices(self):
        self.selected = [[False] * self.cols for _ in range(self.rows)]
        self.processed = [[False] * self.cols for _ in range(self.rows)]
        self.update()

    def sizeHint(self):
        return QSize(int(DISPLAY_PLATE_W), int(DISPLAY_PLATE_H))

    def all_selection(self):
        self.reset_matrices()
        self.invert_selection()

    def invert_selection(self):
        for i in range(self.rows):
            for j in range(self.cols):
                if not self.processed[i][j]:
                    self.selected[i][j] = not self.selected[i][j]
        self.update()

    def restore_defaults(self):
        self.init_params()

    def initializeGL(self):
        glClearColor(1, 1, 1, 1)

    def resizeGL(self, w, h):
        glViewport(0, 0, w, h)
        glMatrixMode(GL_PROJECTION)
        glLoadIdentity()
        glOrtho(0, DISPLAY_PLATE_W, DISPLAY_PLATE_H, 0, -1, 1)
        glMatrixMode(GL_MODELVIEW)

    def paintGL(self):
        glClear(GL_COLOR_BUFFER_BIT)
        glLoadIdentity()
        # Draw plate border
        glColor3f(0, 0, 0); glLineWidth(2)
        glBegin(GL_LINE_LOOP)
        for x, y in [(0,0), (DISPLAY_PLATE_W,0), (DISPLAY_PLATE_W,DISPLAY_PLATE_H), (0,DISPLAY_PLATE_H)]:
            glVertex2f(x, y)
        glEnd()
        # Drill region border
        mx = (DISPLAY_PLATE_W - self.drill_w) / 2
        my = (DISPLAY_PLATE_H - self.drill_h) / 2
        glColor3f(0.2, 0.2, 0.2); glLineWidth(1)
        glBegin(GL_LINE_LOOP)
        for x, y in [(mx,my), (mx+self.drill_w,my), (mx+self.drill_w,my+self.drill_h), (mx,my+self.drill_h)]:
            glVertex2f(x, y)
        glEnd()
        # Draw holes
        cx, cy = DISPLAY_PLATE_W/2, DISPLAY_PLATE_H/2
        offx = -(self.cols - 1)/2 * self.h_spacing
        offy = -(self.rows - 1)/2 * self.v_spacing
        r = self.dia / 2
        for i in range(self.rows):
            for j in range(self.cols):
                x = cx + offx + j*self.h_spacing
                y = cy + offy + i*self.v_spacing
                if self.processed[i][j]:
                    glColor3f(0, 0.8, 0)
                elif self.selected[i][j]:
                    glColor3f(0, 0.8, 0)
                else:
                    glColor3f(0.8, 0, 0)
                self.draw_circle(x, y, r)

    def draw_circle(self, cx, cy, r, segments=32):
        glBegin(GL_TRIANGLE_FAN)
        glVertex2f(cx, cy)
        for k in range(segments + 1):
            theta = 2 * math.pi * k / segments
            glVertex2f(cx + r * math.cos(theta), cy + r * math.sin(theta))
        glEnd()

    def mousePressEvent(self, event):
        if self.rows == 0 or self.cols == 0:
            return
        w, h = self.width(), self.height()
        x_log = event.x() / w * DISPLAY_PLATE_W
        y_log = event.y() / h * DISPLAY_PLATE_H
        cx, cy = DISPLAY_PLATE_W/2, DISPLAY_PLATE_H/2
        offx = -(self.cols - 1)/2 * self.h_spacing
        offy = -(self.rows - 1)/2 * self.v_spacing
        j = int(round((x_log - (cx + offx))/self.h_spacing))
        i = int(round((y_log - (cy + offy))/self.v_spacing))
        if 0 <= i < self.rows and 0 <= j < self.cols:
            if self.processed[i][j]:
                return
            self.selected[i][j] = not self.selected[i][j]
            self.cellClicked.emit(i, j)
            self.update()

    def set_parameters(self, rows, cols, drill_w, drill_h, h_spacing, v_spacing, dia):
        if drill_w > DISPLAY_PLATE_W or drill_h > DISPLAY_PLATE_H:
            QMessageBox.warning(self, "参数超出", "钻孔区域尺寸超出显示板范围！")
            return
        self.rows, self.cols = rows, cols
        self.drill_w, self.drill_h = drill_w, drill_h
        self.h_spacing, self.v_spacing = h_spacing, v_spacing
        self.dia = dia
        self.reset_matrices()

    def mark_processed(self, i, j):
        if i is not None and j is not None:
            self.processed[i][j] = True
            self.selected[i][j] = False
            self.update()