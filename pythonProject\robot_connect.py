import math
from PyQt5.QtCore import QObject, pyqtSignal, QIODevice
from PyQt5.QtNetwork import QTcpSocket, QAbstractSocket
from scipy.spatial.transform import Rotation as R

class RobotClient(QObject):
    # 网络状态信号
    connected       = pyqtSignal()
    disconnected    = pyqtSignal()
    errorOccurred   = pyqtSignal(str)
    responseReceived= pyqtSignal(str)

    def __init__(self, ip: str, port: int, parent=None):
        super().__init__(parent)
        self.ip   = ip
        self.port = port
        self.socket = QTcpSocket(self)
        # 绑定底层信号
        self.socket.connected.connect(self._on_connected)
        self.socket.disconnected.connect(self._on_disconnected)
        self.socket.errorOccurred.connect(lambda err: self._on_error(self.socket.errorString()))
        self.socket.readyRead.connect(self._on_ready_read)
        self.connect_to_server()
    def connect_to_server(self):
        """发起 TCP 连接（异步）"""
        if self.socket.state() in (QTcpSocket.ConnectedState, QTcpSocket.ConnectingState):
            return
        self.socket.connectToHost(self.ip, self.port)

    def close(self):
        """关闭连接"""
        if self.socket.state() != QAbstractSocket.UnconnectedState:
            self.socket.disconnectFromHost()

    def send_command(self, cmd: str):
        """发送指令，收到回复后通过 responseReceived 信号通知"""
        if self.socket.state() != QAbstractSocket.ConnectedState:
            raise RuntimeError("Socket is not connected.")
        data = cmd.encode('utf-8')
        self.socket.write(data)
        self.socket.flush()

    def _on_connected(self):
        self.connected.emit()

    def _on_disconnected(self):
        self.disconnected.emit()

    def _on_error(self, msg: str):
        print("网络错误：", msg)

        self.errorOccurred.emit(msg)

    def _on_ready_read(self):
        """读取所有可用数据，并触发 responseReceived"""
        data = bytes()
        while self.socket.bytesAvailable():
            data += self.socket.read(self.socket.bytesAvailable())
        try:
            resp = data.decode('utf-8')
        except UnicodeDecodeError:
            resp = ""
        if resp:
            self.responseReceived.emit(resp)

    @staticmethod
    def to_euler(data_str: str):
        """将 'x,y,z,rw,rx,ry,rz' 转为 [x,y,z,roll,pitch,yaw]"""
        vals = list(map(float, data_str.split(',')))
        x, y, z, rw, rx, ry, rz = vals
        # 注意：OpenAI例子里四元数顺序为 (x,y,z,w)
        quat = [rx if abs(rx)>=1e-4 else 0,
                ry if abs(ry)>=1e-4 else 0,
                rz if abs(rz)>=1e-4 else 0,
                rw if abs(rw)>=1e-4 else 0]
        r = R.from_quat(quat)
        roll, pitch, yaw = r.as_euler('xyz', degrees=True)
        return [x, y, z, roll, pitch, yaw]

    @staticmethod
    def to_quaternion(data_str: str) -> str:
        """将 'x,y,z,roll,pitch,yaw' 转为 'x,y,z,w,qx,qy,qz'"""
        x, y, z, roll, pitch, yaw = map(float, data_str.split(','))
        r = R.from_euler('xyz', [roll, pitch, yaw], degrees=True)
        qx, qy, qz, qw = r.as_quat()
        return f"{x},{y},{z},{qw},{qx},{qy},{qz}"

    def get_current_position(self):
        """请求当前位置，结果由 responseReceived 发回后用 to_euler 解析"""
        self.send_command("curpos")

    def move_to_pose(self, velocity: int, cnt: int, pose_str: str):
        """异步发 movep 指令"""
        header = f"movep:{velocity:03d}:{cnt}:"
        quat = self.to_quaternion(pose_str)
        self.send_command(header + quat)

    def drill(self, velocity: int, tool: str, wobj: str, pose_str: str):
        """异步发 drill 指令"""
        cmd = f"drill:{velocity:03d}:{tool}:{wobj};{pose_str};"
        self.send_command(cmd)
